<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytTransportWaybillExMapper">
    <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytTransportWaybillEx">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <id column="ex_type" jdbcType="CHAR" property="exType"/>
        <result column="start_point" jdbcType="VARCHAR" property="startPoint"/>
        <result column="dest_point" jdbcType="VARCHAR" property="destPoint"/>
        <result column="task_content" jdbcType="VARCHAR" property="taskContent"/>
        <result column="tel" jdbcType="VARCHAR" property="tel"/>
        <result column="pub_time" jdbcType="VARCHAR" property="pubTime"/>
        <result column="ctime" jdbcType="TIMESTAMP" property="ctime"/>
        <result column="upload_cellphone" jdbcType="VARCHAR" property="uploadCellphone"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="linkman" jdbcType="VARCHAR" property="linkman"/>
        <result column="tel3" jdbcType="VARCHAR" property="tel3"/>
        <result column="tel4" jdbcType="VARCHAR" property="tel4"/>
        <result column="ts_order_no" jdbcType="VARCHAR" property="tsOrderNo"/>
        <result column="pay_user_id" jdbcType="BIGINT" property="payUserId"/>
        <result column="pay_cell_phone" jdbcType="VARCHAR" property="payCellPhone"/>
        <result column="pay_link_phone" jdbcType="VARCHAR" property="payLinkPhone"/>
        <result column="pay_amount" jdbcType="BIGINT" property="payAmount"/>
        <result column="ex_time" jdbcType="TIMESTAMP" property="exTime"/>
        <result column="ex_party" jdbcType="CHAR" property="exParty"/>
        <result column="ex_type_other" jdbcType="CHAR" property="exTypeOther"/>
        <result column="ex_fault_party" jdbcType="CHAR" property="exFaultParty"/>
        <result column="ex_other" jdbcType="VARCHAR" property="exOther"/>
        <result column="ex_status" jdbcType="CHAR" property="exStatus"/>
        <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime"/>
        <result column="result_opinion" jdbcType="VARCHAR" property="resultOpinion"/>
        <result column="car_amount" jdbcType="BIGINT" property="carAmount"/>
        <result column="goods_amount" jdbcType="BIGINT" property="goodsAmount"/>
        <result column="action_user_id" jdbcType="BIGINT" property="actionUserId"/>
        <result column="action_cell_phone" jdbcType="VARCHAR" property="actionCellPhone"/>
        <result column="action_name" jdbcType="VARCHAR" property="actionName"/>
        <result column="mtime" jdbcType="TIMESTAMP" property="mtime"/>
        <result column="ts_id" jdbcType="BIGINT" property="tsId"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="latest_opinion" jdbcType="VARCHAR" property="latestOpinion"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="order_type" jdbcType="INTEGER" property="orderType"/>
    </resultMap>

    <select id="getMyComplaintList" resultType="com.tyt.infofee.bean.MyComplaintListBean">
        select tto.ts_order_no         tsOrderNo,
               tto.start_point         startPoint,
               tto.dest_point          destPoint,
               tto.pub_user_name       pubUserName,
               tto.pay_user_name       payUserName,
               tto.cost_status         costStatus,
               tto.pay_end_time        payEndTime,
               tto.refund_flag         refundFlag,
               tto.delay_status        delayStatus,
               tto.user_id             userId,
               tto.delay_refund_status delayRefundStatus,
               tto.de_refund_dueDate   deRefundDueDate,
               tw.ctime                ctime,
               tw.ex_time              exTime,
               tw.ex_status            exStatus,
               tw.order_id             orderId
        from tyt_transport_waybill_ex tw
                 left join tyt_transport_orders tto
                           on tw.ts_order_no = tto.ts_order_no
        where tw.order_type = 1 and tto.is_complaint = 1
        <if test="payUserId != null">
            and tw.pay_user_id = #{payUserId}
        </if>
        <if test="userId != null">
            and tw.user_id = #{userId}
        </if>

        order by tw.ex_time desc
    </select>
</mapper>
