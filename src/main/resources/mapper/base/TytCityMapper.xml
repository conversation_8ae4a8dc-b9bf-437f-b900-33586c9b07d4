<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytCityMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytCity">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="area_code" jdbcType="VARCHAR" property="areaCode" />
    <result column="parent_area_code" jdbcType="VARCHAR" property="parentAreaCode" />
    <result column="level" jdbcType="VARCHAR" property="level" />
    <result column="map_code" jdbcType="VARCHAR" property="mapCode" />
    <result column="area_name" jdbcType="VARCHAR" property="areaName" />
    <result column="map_area_name" jdbcType="VARCHAR" property="mapAreaName" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="map_city_name" jdbcType="VARCHAR" property="mapCityName" />
    <result column="province_name" jdbcType="VARCHAR" property="provinceName" />
    <result column="map_province_name" jdbcType="VARCHAR" property="mapProvinceName" />
    <result column="rf" jdbcType="VARCHAR" property="rf" />
    <result column="px" jdbcType="VARCHAR" property="px" />
    <result column="py" jdbcType="VARCHAR" property="py" />
    <result column="longitude" jdbcType="VARCHAR" property="longitude" />
    <result column="latitude" jdbcType="VARCHAR" property="latitude" />
    <result column="short_name" jdbcType="VARCHAR" property="shortName" />
    <result column="standard_name" jdbcType="VARCHAR" property="standardName" />
    <result column="map_longitude" jdbcType="VARCHAR" property="mapLongitude" />
    <result column="map_latitude" jdbcType="VARCHAR" property="mapLatitude" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <!-- 通用查询结果列 -->
  <sql id="Base_Column_List">
    id, area_code, parent_area_code, level, map_code, area_name, map_area_name, city_name, map_city_name, province_name, map_province_name, rf, px, py, longitude, latitude, short_name, standard_name, map_longitude, map_latitude, type, create_time
  </sql>


  <select id="selectByAddress" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from tyt_city
    where map_province_name =#{province,jdbcType=VARCHAR} and  map_city_name = #{city,jdbcType=VARCHAR} and map_area_name =#{area,jdbcType=VARCHAR} limit 1
  </select>

  <select id="getCityByName" resultMap="BaseResultMap">
    select * from tyt_city
    where
      level = 3
      and (city_name = #{cityName} or map_city_name = #{cityName})
      and (area_name = #{areaName} or map_area_name = #{areaName})
    limit 1
  </select>

  <select id="getCityByPxPy" resultMap="BaseResultMap">
    select * from tyt_city
    where
      level = 3
      and px = #{px}
      and py = #{py}
      limit 1
  </select>

  <select id="getShortCityName" resultMap="BaseResultMap">
    select * from tyt_city
    where map_city_name = #{cityName}
    and level = 2
    limit 1
  </select>

  <select id="getCityDataByCondition" resultMap="BaseResultMap">
    select *
    from tyt_city where 1 = 1
    <if test=" tytCityDto.provinceName != null and tytCityDto.provinceName != ''">
      and province_name = #{tytCityDto.provinceName}
    </if>
    <if test=" tytCityDto.cityName != null and tytCityDto.cityName != ''">
      and city_name = #{tytCityDto.cityName}
    </if>
    <if test=" tytCityDto.areaName != null and tytCityDto.areaName != ''">
      and area_name = #{tytCityDto.areaName}
    </if>
    limit 1
  </select>

  <select id="getCity" resultMap="BaseResultMap">
    select city_name from tyt_city where level = 2 and city_name like CONCAT('%', #{city},'%')
  </select>

</mapper>
