<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytTransportDispatchMapper">
    <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytTransportDispatch">

        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="src_msg_id" jdbcType="BIGINT" property="srcMsgId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="publish_user_id" jdbcType="BIGINT" property="publishUserId"/>
        <result column="publish_user_name" jdbcType="VARCHAR" property="publishUserName"/>
        <result column="dispatcher_id" jdbcType="BIGINT" property="dispatcherId"/>
        <result column="dispatcher_name" jdbcType="VARCHAR" property="dispatcherName"/>
        <result column="owner_freight" jdbcType="DECIMAL" property="ownerFreight"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="give_goods_phone" jdbcType="VARCHAR" property="giveGoodsPhone"/>
    </resultMap>

    <sql id="baseSql">
        id,
        src_msg_id,
        user_id,
        publish_user_id,
        publish_user_name,
        dispatcher_id,
        dispatcher_name,
        owner_freight,
        create_time,
        modify_time,
        give_goods_phone,
        give_goods_name
    </sql>
    <select id="getTytTransportDispatchBySrcId" resultMap="BaseResultMap">
        select
        <include refid="baseSql"/>
        from tyt_transport_dispatch
        where src_msg_id = #{srcMsgId}
    </select>

    <select id="getDispatchAndGoodsInfo" resultType="com.tyt.plat.entity.base.TytTransportVO">
        select ttd.dispatcher_id  as dispatchId,
               ttd.src_msg_id     as srcMsgId,
               ttm.start_point    as startPoint,
               ttm.dest_point     as destPoint,
               ttm.task_content   as taskContent,
               ttm.user_show_name as userShowName
        from tyt_transport_dispatch ttd
                 left join tyt_transport_main ttm on ttd.src_msg_id = ttm.src_msg_id
        where ttd.src_msg_id = #{srcMsgId}
    </select>
    <select id="countExcellentByPhone" resultType="java.lang.Integer">
        select count(1)
        from tyt_transport_dispatch
        where give_goods_phone = #{cellPhone}
          and create_time &gt;= #{startTime}
          and create_time &lt;= #{endTime}
    </select>
</mapper>