<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.deposit.mapper.base.TytDepositOrderMapper">
    <resultMap id="BaseResultMap" type="com.tyt.deposit.entity.base.TytDepositOrder">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="order_id" jdbcType="VARCHAR" property="orderId"/>
        <result column="deposit_no" jdbcType="VARCHAR" property="depositNo"/>
        <result column="operate_type" jdbcType="INTEGER" property="operateType"/>
        <result column="trade_amount" jdbcType="DECIMAL" property="tradeAmount"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="business_no" jdbcType="VARCHAR" property="businessNo"/>
        <result column="ctime" jdbcType="TIMESTAMP" property="ctime"/>
        <result column="mtime" jdbcType="TIMESTAMP" property="mtime"/>
    </resultMap>


    <sql id="Base_Column_List">
    id, user_id, order_id, deposit_no, operate_type, trade_amount, status, business_no,
    remark, ctime, mtime
  </sql>

    <select id="selectDepositOrderListByUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tyt_deposit_order
        where user_id=#{userId,jdbcType=BIGINT}
    </select>


</mapper>