<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytCourseCategoryMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytCourseCategory">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="category_name" jdbcType="VARCHAR" property="categoryName" />
    <result column="icon_url" jdbcType="VARCHAR" property="iconUrl" />
    <result column="enable" jdbcType="INTEGER" property="enable" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="sort_number" jdbcType="INTEGER" property="sortNumber" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_man_id" jdbcType="BIGINT" property="createManId" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="modify_man_id" jdbcType="BIGINT" property="modifyManId" />
    <result column="modify_name" jdbcType="VARCHAR" property="modifyName" />
  </resultMap>

    <select id="getCategoryList" resultMap="BaseResultMap">
      select
        id,
        type,
        category_name,
        icon_url,
        enable,
        status,
        sort_number,
        remark
      from tyt_course_category
      where status = 1
        and enable = 1
        and type = #{type}
      order by sort_number desc
    </select>

</mapper>