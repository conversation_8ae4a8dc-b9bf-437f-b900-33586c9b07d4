<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.GoodsRefreshManualMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.GoodsRefreshManualDO">
        <id column="id" property="id" />
        <result column="src_msg_id" property="srcMsgId" />
        <result column="user_id" property="userId" />
        <result column="refresh_interval" property="refreshInterval" />
        <result column="total_times" property="totalTimes" />
        <result column="refresh_times" property="refreshTimes" />
        <result column="item_value" property="itemValue" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, src_msg_id, user_id, refresh_interval, total_times, refresh_times, item_value, remark, create_time, modify_time
    </sql>

    <!-- 获取最近7天的曝光动态 -->
    <select id="getRecentlyDynamic" resultType="string">
        SELECT remark FROM tyt_goods_refresh_manual
        WHERE create_time > DATE_SUB(CURDATE(), INTERVAL 7 DAY)
        ORDER BY id DESC
        LIMIT 30
    </select>

    <select id="getBySrcMsgId" resultMap="BaseResultMap">
        select * from tyt_goods_refresh_manual
        where src_msg_id = #{srcMsgId}
    </select>

</mapper>
