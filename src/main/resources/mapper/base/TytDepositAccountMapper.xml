<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.deposit.mapper.base.TytDepositAccountMapper">
    <resultMap id="BaseResultMap" type="com.tyt.deposit.entity.base.TytDepositAccount">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="balance" jdbcType="DECIMAL" property="balance"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="ctime" jdbcType="TIMESTAMP" property="ctime"/>
        <result column="mtime" jdbcType="TIMESTAMP" property="mtime"/>
    </resultMap>

    <sql id="Base_Column_List">
     id, user_id, balance, type, remark, ctime, mtime
    </sql>

    <select id="selectDepositAccountByUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tyt_deposit_account
        where user_id=#{userId,jdbcType=BIGINT}
    </select>


    <select id="selectDepositAccountByUserIdAndType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tyt_deposit_account
        where user_id=#{userId,jdbcType=BIGINT}

        <if test="type !=null and type !=''">
            and type= #{type}
        </if>

    </select>


    <update id="upUserDepositAccount">
        update tyt_deposit_account
        set operator = #{operatorName,jdbcType=VARCHAR},
        operator_id = #{operatorId},

        <if test="isJoin !=null and isJoin !='' ">
            is_join=#{isJoin},
        </if>
        <if test="isDelete !=null and isDelete !='' ">
            is_delete=#{isDelete},
        </if>
        mtime = now()
        where id = #{id,jdbcType=BIGINT}
    </update>


</mapper>