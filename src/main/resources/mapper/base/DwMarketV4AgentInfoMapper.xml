<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.DwMarketV4AgentInfoMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.DwMarketV4AgentInfo">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="source" jdbcType="TINYINT" property="source" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="area" jdbcType="VARCHAR" property="area" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="contact" jdbcType="VARCHAR" property="contact" />
    <result column="meet" jdbcType="VARCHAR" property="meet" />
    <result column="cell_phone" jdbcType="VARCHAR" property="cellPhone" />
    <result column="cell_phone_f" jdbcType="VARCHAR" property="cellPhoneF" />
    <result column="cell_phone_s" jdbcType="VARCHAR" property="cellPhoneS" />
    <result column="role" jdbcType="TINYINT" property="role" />
    <result column="creator_id" jdbcType="BIGINT" property="creatorId" />
    <result column="changer_id" jdbcType="BIGINT" property="changerId" />
    <result column="position" jdbcType="VARCHAR" property="position" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="etime" jdbcType="TIMESTAMP" property="etime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="top_status" jdbcType="TINYINT" property="topStatus" />
    <result column="cooperate_status" jdbcType="TINYINT" property="cooperateStatus" />
    <result column="show_status" jdbcType="TINYINT" property="showStatus" />
    <result column="treat_status" jdbcType="TINYINT" property="treatStatus" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="reject_reason" jdbcType="VARCHAR" property="rejectReason" />
    <result column="longitude" jdbcType="DECIMAL" property="longitude" />
    <result column="latitude" jdbcType="DECIMAL" property="latitude" />
  </resultMap>


  <select id="getByPhone" resultMap="BaseResultMap">
        select * from dw_market_v4_agent_info where cell_phone = #{phone} limit 1
  </select>
</mapper>