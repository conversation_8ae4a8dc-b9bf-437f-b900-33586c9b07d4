<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytInvoiceEnterpriseSignMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytInvoiceEnterpriseSign">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="enterprise_id" jdbcType="BIGINT" property="enterpriseId" />
    <result column="enterprise_name" jdbcType="VARCHAR" property="enterpriseName" />
    <result column="enterprise_credit_code" jdbcType="VARCHAR" property="enterpriseCreditCode" />
    <result column="legal_person_name" jdbcType="VARCHAR" property="legalPersonName" />
    <result column="legal_person_phone" jdbcType="VARCHAR" property="legalPersonPhone" />
    <result column="legal_person_card" jdbcType="VARCHAR" property="legalPersonCard" />
    <result column="enable" jdbcType="INTEGER" property="enable" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="user_phone" jdbcType="VARCHAR" property="userPhone" />
    <result column="contract_status" jdbcType="INTEGER" property="contractStatus" />
    <result column="contract_blank_url" jdbcType="VARCHAR" property="contractBlankUrl" />
    <result column="contract_name" jdbcType="VARCHAR" property="contractName" />
    <result column="contract_number" jdbcType="VARCHAR" property="contractNumber" />
    <result column="contract_start_time" jdbcType="TIMESTAMP" property="contractStartTime" />
    <result column="contract_end_time" jdbcType="TIMESTAMP" property="contractEndTime" />
    <result column="sign_account_id" jdbcType="VARCHAR" property="signAccountId" />
    <result column="sign_seal_url" jdbcType="VARCHAR" property="signSealUrl" />
    <result column="sign_flow_id" jdbcType="VARCHAR" property="signFlowId" />
    <result column="signer_verify_status" jdbcType="INTEGER" property="signerVerifyStatus" />
    <result column="sign_type" jdbcType="INTEGER" property="signType" />
    <result column="active_auth_status" jdbcType="INTEGER" property="activeAuthStatus" />
    <result column="sign_confirm_status" jdbcType="INTEGER" property="signConfirmStatus" />
    <result column="sign_confirm_time" jdbcType="TIMESTAMP" property="signConfirmTime" />
    <result column="sign_auth_url" jdbcType="VARCHAR" property="signAuthUrl" />
    <result column="sign_auth_time" jdbcType="TIMESTAMP" property="signAuthTime" />
    <result column="contract_sign_url" jdbcType="VARCHAR" property="contractSignUrl" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="sign_service_id" jdbcType="VARCHAR" property="signServiceId" />
    <result column="enterprise_flow_id" jdbcType="VARCHAR" property="enterpriseFlowId" />
  </resultMap>

    <select id="getEnableEnterpriseSign" resultMap="BaseResultMap">
      SELECT * FROM `tyt_invoice_enterprise_sign`
      where enterprise_id = #{enterpriseId}
        and enable = 1
    </select>

    <select id="getUserEnterpriseSign" resultMap="BaseResultMap">
      SELECT * FROM `tyt_invoice_enterprise_sign`
      where user_id = #{userId}
        and enable = 1
    </select>

</mapper>