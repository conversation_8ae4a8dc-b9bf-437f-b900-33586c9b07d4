<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.ExposurePermissionExpiredRecordMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.ExposurePermissionExpiredRecord">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="expired_before_num" jdbcType="INTEGER" property="expiredBeforeNum" />
    <result column="expired_num" jdbcType="INTEGER" property="expiredNum" />
    <result column="expired_after_num" jdbcType="INTEGER" property="expiredAfterNum" />
    <result column="expired_time" jdbcType="TIMESTAMP" property="expiredTime" />
  </resultMap>

  <select id="getExposurePermissionExpiredRecordListByUserId" resultMap="BaseResultMap">
    SELECT id, user_id, expired_before_num, expired_num, expired_after_num, expired_time FROM exposure_permission_expired_record
    where user_id = #{userId}
      and expired_time >= DATE_SUB(#{today}, INTERVAL 6 DAY)
    order by expired_time desc
  </select>
</mapper>