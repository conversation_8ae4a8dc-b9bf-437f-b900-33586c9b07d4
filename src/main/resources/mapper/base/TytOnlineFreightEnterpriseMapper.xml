<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytOnlineFreightEnterpriseMapper">
    <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytOnlineFreightEnterprise">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="enterprise_name" jdbcType="VARCHAR" property="enterpriseName"/>
        <result column="legal_person_name" jdbcType="VARCHAR" property="legalPersonName"/>
        <result column="enterprise_credit_code" jdbcType="VARCHAR" property="enterpriseCreditCode"/>
        <result column="enterprise_detail_address" jdbcType="VARCHAR" property="enterpriseDetailAddress"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <select id="getOnlineFreightEnterpriseByName" resultMap="BaseResultMap">
        select id,
               enterprise_name,
               legal_person_name,
               enterprise_credit_code,
               enterprise_detail_address,
               create_time,
               update_time
        from tyt_online_freight_enterprise
        where enterprise_name = #{enterpriseName}
        order by create_time desc limit 1
    </select>

    <select id="getOnlineFreightEnterpriseByEnterpriseCreditCode" resultMap="BaseResultMap">
        select id,
               enterprise_name,
               legal_person_name,
               enterprise_credit_code,
               enterprise_detail_address,
               create_time,
               update_time
        from tyt_online_freight_enterprise
        where enterprise_credit_code = #{enterpriseCreditCode}
        order by create_time desc limit 1
    </select>

</mapper>