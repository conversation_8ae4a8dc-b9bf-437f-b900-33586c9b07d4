<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytGrantStrategyLogMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytGrantStrategyLog">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="grant_strategy_id" jdbcType="BIGINT" property="grantStrategyId" />
    <result column="grant_type" jdbcType="VARCHAR" property="grantType" />
    <result column="gain_type" jdbcType="VARCHAR" property="gainType" />
    <result column="gain_num" jdbcType="INTEGER" property="gainNum" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <select id="selectCountByUserId"  resultType="int">
    select count(*) from tyt_grant_strategy_log
    <where>
      <if test="grantStrategyId != null">
        and grant_strategy_id = #{grantStrategyId}
      </if>
      <if test="userId != null">
        and user_id = #{userId}
      </if>
    </where>
  </select>
</mapper>