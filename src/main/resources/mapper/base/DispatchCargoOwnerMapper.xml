<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.DispatchCargoOwnerMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.DispatchCargoOwner">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="cell_phone" jdbcType="VARCHAR" property="cellPhone" />
    <result column="owner_name" jdbcType="VARCHAR" property="ownerName" />
    <result column="tel" jdbcType="VARCHAR" property="tel" />
    <result column="owner_type" jdbcType="INTEGER" property="ownerType" />
    <result column="sign_partner" jdbcType="INTEGER" property="signPartner" />
    <result column="cooperative_id" jdbcType="BIGINT" property="cooperativeId" />
    <result column="assign_car" jdbcType="INTEGER" property="assignCar" />
    <result column="empower_status" jdbcType="INTEGER" property="empowerStatus" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>

  <sql id="base_columns">
      id,
      user_id as userId,
      cell_phone as  cellPhone,
      owner_name as ownerName,
      tel,
      owner_type as ownerType,
      sign_partner as signPartner,
      cooperative_id as cooperativeId,
      assign_car as assignCar,
      empower_status as empowerStatus,
      remark,
      status,
      operator,
      create_time as createTime,
      modify_time as modifyTime
  </sql>

  <select id="selectSignedByUserId"
          resultType="com.tyt.plat.entity.base.DispatchCargoOwner">
      select <include refid="base_columns"/>
      from tyt_dispatch_cargo_owner
      where user_id = #{ownerUserId} and status = 1 and sign_partner = 1 limit 1
  </select>

    <select id="selectByUserId"
            resultType="com.tyt.plat.entity.base.DispatchCargoOwner">
        select <include refid="base_columns"/>
        from tyt_dispatch_cargo_owner
        where user_id = #{ownerUserId} and status = 1 limit 1
    </select>

    <select id="selectCargoOwnerList" resultType="com.tyt.transport.vo.CargoOwnerInfoVo">
        select id as cargoOwnerId,
               owner_name as cargoOwnerName
        from tyt_dispatch_cargo_owner
        where status = 1 and sign_partner = 1
        <if test="cargoOwnerName != null and cargoOwnerName != ''">
            and owner_name like concat("%",#{cargoOwnerName},"%")
        </if>
    </select>

  <select id="selectByOwnerName"
          resultType="com.tyt.plat.entity.base.DispatchCargoOwner">
      select <include refid="base_columns"/>
      from tyt_dispatch_cargo_owner
      where owner_name = #{platCargoOwnerName} and status = 1 limit 1
  </select>

    <select id="selectByCooperativeId" resultType="com.tyt.plat.entity.base.DispatchCargoOwner">
        select <include refid="base_columns"/>
        from tyt_dispatch_cargo_owner
        where cooperative_id = #{cooperativeId} and status = 1 limit 1
    </select>

    <select id="selectRuleCount" resultType="integer">
        select count(1)
        from tyt_dispatch_cargo_owner
        where user_id = #{ownerUserId}
            and (cooperative_id = (select id from tyt_dispatch_cooperative where cooperative_name = '平台' and status = 1 and delete_flag = 0 limit 1)
           or cooperative_id = 0 or cooperative_id is null) and status = 1
    </select>


</mapper>