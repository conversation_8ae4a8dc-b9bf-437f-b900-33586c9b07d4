<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytExcellentPriceConfigMapper">
    <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytExcellentPriceConfig">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="start_provinc" jdbcType="VARCHAR" property="startProvinc"/>
        <result column="start_city" jdbcType="VARCHAR" property="startCity"/>
        <result column="dest_provinc" jdbcType="VARCHAR" property="destProvinc"/>
        <result column="dest_city" jdbcType="VARCHAR" property="destCity"/>
        <result column="min_weight" jdbcType="DECIMAL" property="minWeight"/>
        <result column="max_weight" jdbcType="DECIMAL" property="maxWeight"/>
        <result column="max_distance" jdbcType="DECIMAL" property="maxDistance"/>
        <result column="min_distance" jdbcType="DECIMAL" property="minDistance"/>
        <result column="start_mileage" jdbcType="DECIMAL" property="startMileage"/>
        <result column="start_price" jdbcType="DECIMAL" property="startPrice"/>
        <result column="unit_price" jdbcType="DECIMAL" property="unitPrice"/>
        <result column="adjust_coefficient" jdbcType="DECIMAL" property="adjustCoefficient"/>
        <result column="max_price_coefficient" jdbcType="DECIMAL" property="maxPriceCoefficient"/>
        <result column="faster_coefficient" jdbcType="DECIMAL" property="fasterCoefficient"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="create_id" jdbcType="BIGINT" property="createId"/>
        <result column="create_name" jdbcType="VARCHAR" property="createName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_id" jdbcType="BIGINT" property="modifyId"/>
        <result column="modify_tame" jdbcType="VARCHAR" property="modifyName"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="price_model" jdbcType="VARCHAR" property="priceModel"/>
        <result column="good_type_name" jdbcType="VARCHAR" property="goodTypeName"/>
    </resultMap>

    <select id="getConfig" resultMap="BaseResultMap">
        select *
        from tyt_excellent_price_config
        where status = 1
          and min_distance &lt;= #{req.distance}
          and max_distance &gt; #{req.distance}
          and min_weight &lt;= #{req.goodsWeight}
          and max_weight &gt; #{req.goodsWeight}
          and (
            (    start_provinc = #{req.startProvince} and start_city = #{req.startCity} and dest_provinc = #{req.destProvince} and dest_city = #{req.destCity})
                OR (start_provinc = '全国' AND (dest_provinc = #{req.destProvince} AND (dest_city = #{req.destCity} OR dest_city = '全省')))
                OR (start_provinc = #{req.startProvince} AND start_city = '全省' AND (dest_provinc = #{req.destProvince} AND (dest_city = #{req.destCity} OR dest_city = '全省')))
                OR (start_provinc = #{req.startProvince} AND start_city = #{req.startCity} AND ((dest_provinc = #{req.destProvince} AND (dest_city = #{req.destCity} OR dest_city = '全省')) OR dest_provinc = '全国'))
                OR (start_provinc = '全国' AND dest_provinc = '全国')
            )
        order by modify_time desc, id desc

    </select>
</mapper>