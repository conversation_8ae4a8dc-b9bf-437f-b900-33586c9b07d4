<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytCoverGoodsWhiteListConfigUserMapper">
    <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytCoverGoodsWhiteListConfigUser">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="operate_user_id" jdbcType="BIGINT" property="operateUserId"/>
        <result column="operate_user_name" jdbcType="VARCHAR" property="operateUserName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="dial_config_id" jdbcType="BIGINT" property="dialConfigId"/>
    </resultMap>

    <select id="countByUserId" resultType="java.lang.Long">
        select count(*)
        from tyt_cover_goods_white_list_config_user whiteUser
                 left join tyt_cover_goods_dial_config conf on whiteUser.dial_config_id = conf.id
        where whiteUser.user_id = #{userId}
          and conf.del_flag = 0
          and conf.enable = 1
    </select>

</mapper>