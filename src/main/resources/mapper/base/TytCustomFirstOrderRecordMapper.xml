<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytCustomFirstOrderRecordMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytCustomFirstOrderRecord">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="custom_phone" jdbcType="VARCHAR" property="customPhone" />
    <result column="first_publish_time" jdbcType="TIMESTAMP" property="firstPublishTime" />
    <result column="first_finish_order_time" jdbcType="TIMESTAMP" property="firstFinishOrderTime" />
    <result column="status" jdbcType="TINYINT" property="status" />
  </resultMap>

  <select id="countFinishOrder" resultType="java.lang.Integer">
    select count(1)
    from tyt_custom_first_order_record
    where custom_phone = #{cellPhone}
      and first_finish_order_time is not null
  </select>
</mapper>