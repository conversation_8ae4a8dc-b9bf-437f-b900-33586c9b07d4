<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytMachineTypeAuditMapper">
    <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytMachineTypeAudit">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="show_name" jdbcType="VARCHAR" property="showName"/>
        <result column="top_class" jdbcType="VARCHAR" property="topClass"/>
        <result column="second_class" jdbcType="VARCHAR" property="secondClass"/>
        <result column="brand_type" jdbcType="VARCHAR" property="brandType"/>
        <result column="top_type" jdbcType="VARCHAR" property="topType"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="conformance_counts" jdbcType="INTEGER" property="conformanceCounts"/>
        <result column="illegal_counts" jdbcType="INTEGER" property="illegalCounts"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="create_name" jdbcType="VARCHAR" property="createName"/>
        <result column="modify_name" jdbcType="VARCHAR" property="modifyName"/>
    </resultMap>
    <select id="selectByShowName" resultMap="BaseResultMap">
        select *
        from tyt_machine_type_audit
        where show_name = #{showName}
        limit 1
    </select>
</mapper>