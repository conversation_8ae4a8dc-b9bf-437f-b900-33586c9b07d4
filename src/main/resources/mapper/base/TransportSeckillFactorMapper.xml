<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TransportSeckillFactorMapper">

    <select id="selectAllDestCity" resultType="string">
        select dest_city from tyt_transport_seckill_factor
    </select>

    <select id="existDestCity" resultType="integer">
        select count(1) from tyt_transport_seckill_factor where dest_city = #{destCity}
    </select>

</mapper>
