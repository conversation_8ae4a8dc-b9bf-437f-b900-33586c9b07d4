<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytInvoiceTaxRateRuleMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytInvoiceTaxRateRule">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="config_type" jdbcType="INTEGER" property="configType" />
    <result column="enterprise_id" jdbcType="BIGINT" property="enterpriseId" />
    <result column="earlier_tax_rate" jdbcType="DECIMAL" property="earlierTaxRate" />
    <result column="tiered_tax_rate" jdbcType="VARCHAR" property="tieredTaxRate" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>

    <select id="getInvoiceTaxRateRule" resultMap="BaseResultMap">
      select * from tyt_invoice_tax_rate_rule
      where config_type = #{configType}
        and enterprise_id = #{enterpriseId}
        and status = 1
        limit 1
    </select>
</mapper>