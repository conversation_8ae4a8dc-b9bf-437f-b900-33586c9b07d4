<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.GoodsAddressLevelRecordMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.GoodsAddressLevelRecord">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="src_msg_id" jdbcType="BIGINT" property="srcMsgId" />
    <result column="start_province" jdbcType="VARCHAR" property="startProvince" />
    <result column="start_city" jdbcType="VARCHAR" property="startCity" />
    <result column="start_district" jdbcType="VARCHAR" property="startDistrict" />
    <result column="start_township" jdbcType="VARCHAR" property="startTownship" />
    <result column="start_street" jdbcType="VARCHAR" property="startStreet" />
    <result column="dest_province" jdbcType="VARCHAR" property="destProvince" />
    <result column="dest_city" jdbcType="VARCHAR" property="destCity" />
    <result column="dest_district" jdbcType="VARCHAR" property="destDistrict" />
    <result column="dest_township" jdbcType="VARCHAR" property="destTownship" />
    <result column="dest_street" jdbcType="VARCHAR" property="destStreet" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="modify_name" jdbcType="VARCHAR" property="modifyName" />
    <result column="i_g_b_i_result_data" jdbcType="INTEGER" property="iGBIResultData" />
    <result column="publish_transport_is_show_good_car" jdbcType="INTEGER" property="publishTransportIsShowGoodCar" />
    <result column="start_addr_source" jdbcType="INTEGER" property="startAddrSource" />
    <result column="dest_addr_source" jdbcType="INTEGER" property="destAddrSource" />
    <result column="th_min_price" jdbcType="INTEGER" property="thMinPrice" />
    <result column="th_max_price" jdbcType="INTEGER" property="thMaxPrice" />
    <result column="suggest_price" jdbcType="INTEGER" property="suggestPrice" />
    <result column="client_type" jdbcType="INTEGER" property="clientType" />
    <result column="suggest_min_price" jdbcType="DECIMAL" property="suggestMinPrice" />
    <result column="suggest_max_price" jdbcType="DECIMAL" property="suggestMaxPrice" />
    <result column="fix_price_min" jdbcType="INTEGER" property="fixPriceMin" />
    <result column="fix_price_max" jdbcType="INTEGER" property="fixPriceMax" />
    <result column="fix_price_fast" jdbcType="INTEGER" property="fixPriceFast" />
    <result column="show_good_car_price_transport_tab" jdbcType="INTEGER" property="showGoodCarPriceTransportTab" />
    <result column="automatic_good_car_price_transport_type" jdbcType="INTEGER" property="automaticGoodCarPriceTransportType" />
    <result column="good_car_price_transport" jdbcType="INTEGER" property="goodCarPriceTransport" />
    <result column="x_time_in_config" jdbcType="INTEGER" property="xTimeInConfig" />
    <result column="x_time_in_actual" jdbcType="INTEGER" property="xTimeInActual" />
    <result column="publish_type" jdbcType="TINYINT" property="publishType" />
    <result column="distance_kilometer" jdbcType="DECIMAL" property="distanceKilometer" />
    <result column="other_fee" jdbcType="DECIMAL" property="otherFee" />
    <result column="commission_transport" jdbcType="INTEGER" property="commissionTransport" />
    <result column="meet_commission_rules" jdbcType="INTEGER" property="meetCommissionRules" />
    <result column="change_goods_type" jdbcType="INTEGER" property="changeGoodsType" />
  </resultMap>

  <select id="selectBySrcMsgId" resultMap="BaseResultMap">
    select *
    from goods_address_level_record
    where src_msg_id = #{srcMsgId}
  </select>
</mapper>