<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.FeedbackUserNegativeLabelConfigMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.FeedbackUserNegativeLabelConfig">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="user_phone" jdbcType="VARCHAR" property="userPhone" />
    <result column="show_feedback_label_id" jdbcType="BIGINT" property="showFeedbackLabelId" />
    <result column="is_show" jdbcType="SMALLINT" property="isShow" />
    <result column="modify_user_id" jdbcType="BIGINT" property="modifyUserId" />
    <result column="modify_user_name" jdbcType="VARCHAR" property="modifyUserName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <select id="selectByUserId" resultMap="BaseResultMap">
    select *
    from feedback_user_negative_label_config
    where user_id = #{userId}
  </select>
</mapper>