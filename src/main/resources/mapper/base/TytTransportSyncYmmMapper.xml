<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytTransportSyncYmmMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytTransportSyncYmm">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="src_msg_id" jdbcType="BIGINT" property="srcMsgId" />
    <result column="cargo_id" jdbcType="BIGINT" property="cargoId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="partner_serial_no" jdbcType="VARCHAR" property="partnerSerialNo" />
    <result column="transport_number" jdbcType="VARCHAR" property="transportNumber" />
    <result column="transport_status" jdbcType="INTEGER" property="transportStatus" />
    <result column="sync_status" jdbcType="INTEGER" property="syncStatus" />
    <result column="sub_code" jdbcType="VARCHAR" property="subCode" />
    <result column="sub_code_msg" jdbcType="VARCHAR" property="subCodeMsg" />
    <result column="tracking_msg" jdbcType="VARCHAR" property="trackingMsg" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
  </resultMap>

  <select id="selectTytTransportSyncYmmBySrcId" resultMap="BaseResultMap">
    select * from tyt_transport_sync_ymm where is_delete = 0 and src_msg_id = #{srcMsgId} and transport_status = 0 and sync_status = 0 order by id desc limit 1
  </select>

</mapper>