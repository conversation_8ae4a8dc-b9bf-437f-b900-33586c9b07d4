<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytTecServiceFeeStageConfigMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytTecServiceFeeStageConfig">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="config_id" jdbcType="BIGINT" property="configId" />
    <result column="price_min" jdbcType="DECIMAL" property="priceMin" />
    <result column="price_max" jdbcType="DECIMAL" property="priceMax" />
    <result column="tec_service_fee_rate" jdbcType="DECIMAL" property="tecServiceFeeRate" />
    <result column="tec_service_fee_min" jdbcType="DECIMAL" property="tecServiceFeeMin" />
    <result column="tec_service_fee_max" jdbcType="DECIMAL" property="tecServiceFeeMax" />
    <result column="discount" jdbcType="DECIMAL" property="discount" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="type" jdbcType="INTEGER" property="type" />
  </resultMap>

  <!-- SELECT (READ) -->
  <select id="getByConfigIdAndType" resultMap="BaseResultMap">
    SELECT * FROM tyt_tec_service_fee_stage_config WHERE config_id = #{configId} and type = #{type}
  </select>

</mapper>