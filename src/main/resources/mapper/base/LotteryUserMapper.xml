<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.LotteryUserMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.LotteryUser">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="total_draws_num" jdbcType="BIGINT" property="totalDrawsNum" />
    <result column="draw_activity_info_id" jdbcType="BIGINT" property="drawActivityInfoId" />
    <result column="user_call_phone" jdbcType="VARCHAR" property="userCallPhone" />
    <result column="take_in" jdbcType="TINYINT" property="takeIn" />
    <result column="whether_to_send" jdbcType="TINYINT" property="whetherToSend" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <select id="countByUserIdAndDrawActivityId" resultType="long">
    select count(*) from  lottery_user where user_id=#{userId} and  draw_activity_info_id= #{drawActivityId} and is_delete = 1
    </select>
</mapper>