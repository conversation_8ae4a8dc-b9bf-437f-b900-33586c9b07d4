<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.ExposurePermissionGainRecordMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.ExposurePermissionGainRecord">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="gain_type" jdbcType="INTEGER" property="gainType" />
    <result column="ord_num" jdbcType="VARCHAR" property="ordNum" />
    <result column="goods_id" jdbcType="BIGINT" property="goodsId" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="expired_time" jdbcType="TIMESTAMP" property="expiredTime" />
    <result column="total_num" jdbcType="INTEGER" property="totalNum" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
  </resultMap>

  <select id="getExposurePermissionGainRecordListByUserId" resultMap="BaseResultMap">
    SELECT id, user_id, gain_type, ord_num, goods_id, goods_name, expired_time, total_num, ctime FROM exposure_permission_gain_record
    where user_id = #{userId}
      and ctime >= DATE_SUB(#{today}, INTERVAL 6 DAY)
    order by ctime desc
  </select>

  <select id="selectUserCenterExposureInfo" resultType="com.tyt.acvitity.bean.UserCenterExposureInfo">
    SELECT
      gainType,exposureNum,
      CASE
		gainType
		WHEN 0 THEN '购买'
		WHEN 1 THEN '新用户关怀'
		WHEN 2 THEN '老用户回归'
		WHEN 3 THEN '老用户回归'
		WHEN 4 THEN '信用等级发放'
		WHEN 5 THEN '履约获取'
		WHEN 6 THEN '购买会员'
		WHEN 7 THEN '货源保障'
		WHEN 8 THEN '活动获取'
		WHEN 9 THEN '发布有价货源'
		ELSE '其他'
    END as gainTypeName
    FROM
      (
        SELECT
          gain_type AS gainType,
          ctime,
          (SELECT COUNT(id) FROM exposure_permission_gain_record WHERE user_id=#{userId} AND gain_type = gainType AND expired_time >=NOW() AND ctime >=#{startTime,jdbcType=VARCHAR}) AS exposureNum
        FROM
          exposure_permission_gain_record WHERE user_id = #{userId} AND expired_time >=NOW() AND ctime >=#{startTime,jdbcType=VARCHAR}
        ORDER BY
          ctime DESC
      ) z
    GROUP BY
      gainType

    ORDER BY ctime DESC LIMIT 5
  </select>

  <select id="selectLastExposureInfo" resultType="com.tyt.acvitity.bean.UserCenterExposureInfo">
      select
          gain_type AS gainType,
--           CASE
--               gain_type
--               WHEN 0 THEN '购买'
--               WHEN 1 THEN '新用户关怀'
--               WHEN 2 THEN '老用户回归'
--               WHEN 3 THEN '老用户回归'
--               WHEN 4 THEN '信用等级发放'
--               WHEN 5 THEN '履约获取'
--               WHEN 6 THEN '购买会员'
--               WHEN 7 THEN '货源保障'
--               WHEN 8 THEN '活动获取'
--               WHEN 9 THEN '发布有价货源'
--               ELSE '其他'
--               END as gainTypeName,
          total_num as exposureNum
      FROM exposure_permission_gain_record
      WHERE user_id = #{userId} AND expired_time >=NOW() AND ctime >=#{startTime,jdbcType=VARCHAR} order by ctime desc LIMIT 5
    </select>

    <select id="getNewExposureCount" resultType="java.lang.Integer">
        select ifnull(sum(total_num), 0) from exposure_permission_gain_record
        where
            user_id = #{userId}
          and id > #{markId}
          and ctime > #{startTime}
    </select>

    <select id="getMaxGainId" resultType="java.lang.Long">
        select max(id) from exposure_permission_gain_record
        where user_id = #{userId}
    </select>

</mapper>