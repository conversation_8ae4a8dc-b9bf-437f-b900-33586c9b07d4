<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytTransportDispatchCarTypeMapper">
	<resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytTransportDispatchCarType">
		<!--
		  WARNING - @mbg.generated
		-->
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="start_tonnage" jdbcType="INTEGER" property="startTonnage" />
		<result column="end_tonnage" jdbcType="INTEGER" property="endTonnage" />
		<result column="car_type" jdbcType="VARCHAR" property="carType" />
		<result column="status" jdbcType="INTEGER" property="status" />
		<result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
		<result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
	</resultMap>

	<select id="selectCarTypeListByWeight" resultType="java.lang.String">
		select car_type
		from tyt_transport_dispatch_car_type
		where status = 1 and start_tonnage &lt;= #{weight} and end_tonnage &gt;= #{weight}
	</select>
</mapper>