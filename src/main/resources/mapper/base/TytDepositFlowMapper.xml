<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.deposit.mapper.base.TytDepositFlowMapper">
    <resultMap id="BaseResultMap" type="com.tyt.deposit.entity.base.TytDepositFlow">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="deposit_no" jdbcType="VARCHAR" property="depositNo"/>
        <result column="flow_no" jdbcType="VARCHAR" property="flowNo"/>
        <result column="transaction_account_info" jdbcType="VARCHAR" property="transactionAccountInfo"/>
        <result column="direction" jdbcType="INTEGER" property="direction"/>
        <result column="trade_amount" jdbcType="DECIMAL" property="tradeAmount"/>
        <result column="pre_amount" jdbcType="DECIMAL" property="preAmount"/>
        <result column="end_amount" jdbcType="DECIMAL" property="endAmount"/>
        <result column="ctime" jdbcType="TIMESTAMP" property="ctime"/>
    </resultMap>

    <sql id="Base_Column_List">
    id, user_id, deposit_no, flow_no, transaction_account_info, direction, trade_amount, pre_amount,
    end_amount
  </sql>

    <select id="selectDepositFlowListByUserId" resultType="com.tyt.deposit.bean.DepositFlowBean">
        select
         df.id, df.user_id  as userId,df.direction,df.trade_amount as tradeAmount,df.ctime,do.operate_type as operateType,do.ts_order_no as  tsOrderNo
        from tyt_deposit_flow df left join  tyt_deposit_order do on df.deposit_no=do.deposit_no
        where df.user_id=#{userId,jdbcType=BIGINT} order by df.ctime desc
    </select>


</mapper>