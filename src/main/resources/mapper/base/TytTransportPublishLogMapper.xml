<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytTransportPublishLogMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytTransportPublishLog">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="src_msg_id" jdbcType="BIGINT" property="srcMsgId" />
    <result column="pub_type" jdbcType="INTEGER" property="pubType" />
    <result column="pub_time" jdbcType="TIMESTAMP" property="pubTime" />
    <result column="request_source" jdbcType="INTEGER" property="requestSource" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>
</mapper>