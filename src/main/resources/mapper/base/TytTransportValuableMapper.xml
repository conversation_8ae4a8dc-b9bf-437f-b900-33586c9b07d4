<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytTransportValuableMapper">
	<resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytTransportValuable">
		<!--
		  WARNING - @mbg.generated
		-->
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="src_msg_id" jdbcType="BIGINT" property="srcMsgId" />
		<result column="publish_user_id" jdbcType="BIGINT" property="publishUserId" />
		<result column="publish_user_name" jdbcType="VARCHAR" property="publishUserName" />
		<result column="give_goods_phone" jdbcType="VARCHAR" property="giveGoodsPhone" />
		<result column="user_id" jdbcType="BIGINT" property="userId" />
		<result column="owner_freight" jdbcType="DECIMAL" property="ownerFreight" />
		<result column="value_type" jdbcType="INTEGER" property="valueType" />
		<result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
		<result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
		<result column="follow_up" jdbcType="INTEGER" property="followUp" />
		<result column="first_order" jdbcType="INTEGER" property="firstOrder" />
		<result column="top_three_order" jdbcType="INTEGER" property="topThreeOrder" />
		<result column="follow_up_user_id" jdbcType="BIGINT" property="followUpUserId" />
		<result column="follow_up_user_name" jdbcType="VARCHAR" property="followUpUserName" />
		<result column="follow_up_time" jdbcType="TIMESTAMP" property="followUpTime" />
		<result column="market_follow_up_user_id" jdbcType="BIGINT" property="marketFollowUpUserId" />
		<result column="market_follow_up_user_name" jdbcType="VARCHAR" property="marketFollowUpUserName" />
		<result column="driver_give_price" jdbcType="INTEGER" property="driverGivePrice" />
		<result column="driver_latest_price" jdbcType="DECIMAL" property="driverLatestPrice" />
		<result column="direct_customer" jdbcType="INTEGER" property="directCustomer" />
	</resultMap>

	<select id="selectBySrcMsgId" resultMap="BaseResultMap">
		select *
		from tyt_transport_valuable
		where src_msg_id = #{srcMsgId}
		limit 1
	</select>
</mapper>