<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.FeedbackUserOperateLogMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.FeedbackUserOperateLog">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="feedback_id" jdbcType="BIGINT" property="feedbackId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="pre_operate_label" jdbcType="VARCHAR" property="preOperateLabel" />
    <result column="post_operate_label" jdbcType="VARCHAR" property="postOperateLabel" />
    <result column="pre_operate_comment" jdbcType="VARCHAR" property="preOperateComment" />
    <result column="post_operate_comment" jdbcType="VARCHAR" property="postOperateComment" />
    <result column="pre_operate_type" jdbcType="INTEGER" property="preOperateType" />
    <result column="post_operate_type" jdbcType="INTEGER" property="postOperateType" />
    <result column="operate_type" jdbcType="INTEGER" property="operateType" />
  </resultMap>
</mapper>