<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.RequestLogMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.RequestLog">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="src_msg_id" jdbcType="BIGINT" property="srcMsgId" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="create_time" jdbcType="DATE" property="createTime" />
  </resultMap>

  <insert id="addRequestLog">
    insert into tyt_creeper_app_request_log (user_id, src_msg_id, type, create_time) VALUE (#{userId}, #{goodsId}, #{type}, now())
  </insert>

</mapper>