<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.FeedbackUserAppealInfoMapper">

    <select id="getInfoByAppealId" resultType="com.tyt.plat.entity.base.FeedbackUserAppealInfo">
        select id,appeal_id appealId,appeal_reason appealReason,appeal_picture appealPicture
        from tyt.feedback_user_appeal_info where appeal_id = #{appealId} order by id desc limit 1
    </select>
</mapper>