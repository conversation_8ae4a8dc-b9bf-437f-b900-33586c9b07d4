<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.FeedbackUserMapper">
    <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.FeedbackUser">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="post_user_id" jdbcType="BIGINT" property="postUserId"/>
        <result column="post_user_phone" jdbcType="VARCHAR" property="postUserPhone"/>
        <result column="post_user_avatar" jdbcType="VARCHAR" property="postUserAvatar"/>
        <result column="post_user_type" jdbcType="SMALLINT" property="postUserType"/>
        <result column="receive_user_id" jdbcType="BIGINT" property="receiveUserId"/>
        <result column="receive_user_phone" jdbcType="VARCHAR" property="receiveUserPhone"/>
        <result column="receive_user_avatar" jdbcType="VARCHAR" property="receiveUserAvatar"/>
        <result column="receive_user_type" jdbcType="SMALLINT" property="receiveUserType"/>
        <result column="del_flag" jdbcType="BIT" property="delFlag"/>
        <result column="ts_order_id" jdbcType="BIGINT" property="tsOrderId"/>
        <result column="ts_id" jdbcType="BIGINT" property="tsId"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="transport_order_pub_user_name" jdbcType="VARCHAR" property="transportOrderPubUserName"/>
        <result column="transport_order_pay_user_name" jdbcType="VARCHAR" property="transportOrderPayUserName"/>
        <result column="transport_order_start_point" jdbcType="VARCHAR" property="transportOrderStartPoint"/>
        <result column="transport_order_dest_point" jdbcType="VARCHAR" property="transportOrderDestPoint"/>
        <result column="feedback_type" jdbcType="SMALLINT" property="feedbackType"/>
        <result column="feedback_deadline" jdbcType="DATE" property="feedbackDeadline"/>
        <result column="need_handle_on_deadline" jdbcType="BIT" property="needHandleOnDeadline"/>
        <result column="comment" jdbcType="VARCHAR" property="comment"/>
        <result column="update_times" jdbcType="INTEGER" property="updateTimes"/>
        <result column="receiver_hide_flag" jdbcType="INTEGER" property="receiverHideFlag"/>
        <result column="lose_efficacy_reason" jdbcType="INTEGER" property="loseEfficacyReason"/>
    </resultMap>

    <select id="count" resultType="long">
        select count(*)
        from feedback_user
        where create_time > #{beginTime}
        and feedback_type = #{feedbackType}
        and del_flag = #{delFlag}
        <if test="isValid != null and isValid == 1">
            and receiver_hide_flag = 0
        </if>
        <if test="postUserId != null">
            and post_user_id = #{postUserId}
        </if>
        <if test="postUserType != null">
            and post_user_type = #{postUserType}
        </if>
        <if test="receiveUserId != null">
            and receive_user_id = #{receiveUserId}
        </if>
        <if test="receiveUserType != null">
            and receive_user_type = #{receiveUserType}
        </if>
    </select>

    <select id="selectList" resultMap="BaseResultMap">
        select fu.*
        from feedback_user fu
        where fu.create_time &gt; #{beginTime}
          and fu.del_flag = #{delFlag}
        <if test="postUserId != null">
            and fu.post_user_id = #{postUserId}
        </if>
        <if test="postUserType != null">
            and fu.post_user_type = #{postUserType}
        </if>
        <if test="receiveUserId != null">
            and fu.receive_user_id = #{receiveUserId}
        </if>
        <if test="receiveUserType != null">
            and fu.receive_user_type = #{receiveUserType}
        </if>
        <if test="feedbackType != null">
            and fu.feedback_type = #{feedbackType}
        </if>
        <if test="labelId != null">
            and fu.id in (select feedback_id
                          from feedback_user_label ful
                          where fu.id = ful.feedback_id
                            and ful.label_id = #{labelId}
                            and ful.del_flag = #{delFlag})
        </if>
        order by fu.update_time desc
    </select>
</mapper>