<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytSpecialCarRouteMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytSpecialCarRoute">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="special_id" jdbcType="BIGINT" property="specialId" />
    <result column="start_city" jdbcType="VARCHAR" property="startCity" />
    <result column="dest_city" jdbcType="VARCHAR" property="destCity" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>

  <select id="getBySpecialId" resultMap="BaseResultMap">
    select start_city,dest_city from tyt_special_car_route where special_id = #{id}
  </select>

  <delete id="deleteBySpecialId">
    DELETE FROM tyt_special_car_route WHERE special_id = #{id}
  </delete>
</mapper>