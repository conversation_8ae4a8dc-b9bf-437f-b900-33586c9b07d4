<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.ExcellentGoodsGroupMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.ExcellentGoodsGroup">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="group_name" jdbcType="VARCHAR" property="groupName" />
    <result column="goods_count" jdbcType="INTEGER" property="goodsCount" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="enabled" jdbcType="INTEGER" property="enabled" />
    <result column="is_del" jdbcType="INTEGER" property="isDel" />
    <result column="operate_id" jdbcType="BIGINT" property="operateId" />
    <result column="operate_name" jdbcType="VARCHAR" property="operateName" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="call_no_price_count" jdbcType="INTEGER" property="callNoPriceCount" />
    <result column="call_price_count" jdbcType="INTEGER" property="callPriceCount" />
    <result column="fixed_price_count" jdbcType="INTEGER" property="fixedPriceCount" />
  </resultMap>
  <select id="selectUserGroupInfo" resultType="com.tyt.deposit.bean.ExcellentGoodsGroupDetailBean">
    select gg.goods_count goodsCount, gg.type, ggu.user_id userId
         , gg.call_no_price_count callNoPriceCount, gg.call_price_count callPriceCount, gg.fixed_price_count fixedPriceCount
    from excellent_goods_group gg
           left join excellent_goods_group_user ggu on gg.id = ggu.group_id
    where gg.is_del = 1
      and gg.enabled = 1
      and ggu.is_del = 1
      and ggu.user_id = #{userId}
  </select>

</mapper>