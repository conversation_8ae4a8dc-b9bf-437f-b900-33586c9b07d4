<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.deposit.mapper.base.TytGoodsRefreshUserMapper">
    <resultMap id="BaseResultMap" type="com.tyt.deposit.entity.base.TytGoodsRefreshUser">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="refresh_code" jdbcType="VARCHAR" property="refreshCode"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="create_name" jdbcType="VARCHAR" property="createName"/>
        <result column="modify_name" jdbcType="VARCHAR" property="modifyName"/>
    </resultMap>

    <select id="selectExcellentGoodsContentByUserId" resultType="com.tyt.deposit.bean.GoodsRefreshConfigDto">
        select t.content, u.user_id userId
        from tyt_goods_refresh_user u
                 left join tyt_goods_refresh_config t on u.refresh_code = t.code
        where u.user_id = #{userId}
          and t.config_type = 1
          and (excellent_goods = 1 or instant_grab = 1 )
          and t.status = 1
          and t.del = 0
    </select>
</mapper>