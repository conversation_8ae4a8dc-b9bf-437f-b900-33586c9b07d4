<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytSigningCarMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytSigningCar">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="tyt_cell_phone" jdbcType="VARCHAR" property="tytCellPhone" />
    <result column="signing" jdbcType="VARCHAR" property="signing" />
    <result column="dispatch" jdbcType="VARCHAR" property="dispatch" />
    <result column="cooperate_num" jdbcType="INTEGER" property="cooperateNum" />
    <result column="assign_num" jdbcType="INTEGER" property="assignNum" />
    <result column="assign_success_num" jdbcType="INTEGER" property="assignSuccessNum" />
    <result column="receiving_orders" jdbcType="DECIMAL" property="receivingOrders" />
    <result column="favorable_comment" jdbcType="DECIMAL" property="favorableComment" />
    <result column="compre_fraction" jdbcType="DECIMAL" property="compreFraction" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
  </resultMap>

  <select id="getByUserId" resultMap="BaseResultMap">
    select id,status,signing from tyt_signing_car where user_id = #{userId} limit 1

  </select>

  <update id="updateCar">
    update tyt_signing_car set status = #{status} where user_id = #{userId}
  </update>

  <select id="getUserList" resultType="com.tyt.plat.entity.base.TytSigningCarVO">
    select info.id,
    info.driver_id AS driverId,
    info.favorable_comment AS favorableComment,
    info.receiving_orders AS receivingOrders,
    info.head_city_no AS headCityNo,
    car.user_id as userId,
    car.tyt_cell_phone as phone,
    black.sign_status as status
    from tyt_signing_car_info info
    left join tyt_signing_car car on info.signing_id = car.id
    left join tyt_signing_driver_black black on info.driver_user_id = black.user_id
    where  info.id in
    <foreach collection="listIds" item="id" index="index" open="(" close=")" separator=",">
      #{id}
    </foreach>

  </select>
  <select id="getAssignableCars" resultType="com.tyt.plat.vo.other.SigningCarInfoVo">
    select info.id as carInfoId,
           info.driving_ability as drivingAbility,
           info.head_city_no AS headCityNo,
           car.signing as signing,
           car.user_id as userId,
           info.driver_id as driverId,
           info.driver_user_id as driverUserId,
           info.favorable_comment AS favorableComment,
           info.receiving_orders AS receivingOrders
    from tyt_signing_car_info info
    left join tyt_signing_car car on info.signing_id = car.id
    where car.status=1 and info.city in
    <foreach collection="cities" item="city" index="index" open="(" close=")" separator=",">
      #{city}
    </foreach>
  </select>

  <update id="updateDriverAssignNum">
    update tyt_signing_car_info set assign_num = assign_num + 1 where id = #{carInfoId}
  </update>


  <select id="getAssignableCarsByRoute" resultType="com.tyt.plat.vo.other.SigningCarInfoVo">
    select info.id as carInfoId,
           info.driving_ability as drivingAbility,
           info.head_city_no AS headCityNo,
           info.tail_city_no AS tailCityNo,
           info.car_type as carType,
           car.signing as signing,
           car.user_id as userId,
           car.tyt_cell_phone as phone,
           driver.verify_status as verifyStatus,
           info.driver_id as driverId,
           info.driver_user_id as driverUserId,
           info.distance_preference as distancePreference,
           info.driver_tag as driverTag,
           info.favorable_comment AS favorableComment,
           info.receiving_orders AS receivingOrders
    from tyt_signing_car_info info
    left join tyt_invoice_driver driver on info.driver_id = driver.id
    left join tyt_signing_car car on info.signing_id = car.id
    left join tyt_signing_car_info_route route on info.id = route.car_info_id
    where car.status = 1 and route.start_city = #{startCity} and route.dest_city = #{destCity}
  </select>

  <select id="getAssignableCarsByCity" resultType="com.tyt.plat.vo.other.SigningCarInfoVo">
    select info.id as carInfoId,
           info.driving_ability as drivingAbility,
           info.head_city_no AS headCityNo,
           info.tail_city_no AS tailCityNo,
           info.car_type as carType,
           car.signing as signing,
           car.user_id as userId,
           car.tyt_cell_phone as phone,
           driver.verify_status as verifyStatus,
           info.driver_id as driverId,
           info.driver_user_id as driverUserId,
           info.distance_preference as distancePreference,
           info.driver_tag as driverTag,
           info.favorable_comment AS favorableComment,
           info.receiving_orders AS receivingOrders
    from tyt_signing_car_info info
    left join tyt_invoice_driver driver on info.driver_id = driver.id
    left join tyt_signing_car car on info.signing_id = car.id
    left join tyt_signing_car_info_city city on info.id = city.car_info_id
    where car.status = 1 and city.type = 2 and city.city in
    <foreach collection="cities" item="city" index="index" open="(" close=")" separator=",">
      #{city}
    </foreach>
  </select>

  <select id="getAssignableCarsByProvince" resultType="com.tyt.plat.vo.other.SigningCarInfoVo">
      select info.id as carInfoId,
             info.driving_ability as drivingAbility,
             info.head_city_no AS headCityNo,
             info.tail_city_no AS tailCityNo,
             info.car_type as carType,
             car.signing as signing,
             car.user_id as userId,
             car.tyt_cell_phone as phone,
             driver.verify_status as verifyStatus,
             info.driver_id as driverId,
             info.driver_user_id as driverUserId,
             info.distance_preference as distancePreference,
             info.driver_tag as driverTag,
             info.favorable_comment AS favorableComment,
             info.receiving_orders AS receivingOrders
      from tyt_signing_car_info info
      left join tyt_invoice_driver driver on info.driver_id = driver.id
      left join tyt_signing_car car on info.signing_id = car.id
      left join tyt_signing_car_info_city city on info.id = city.car_info_id
      where car.status = 1 and city.type = 1 and city.city in
      <foreach collection="provinces" item="province" index="index" open="(" close=")" separator=",">
          #{province}
      </foreach>
  </select>

  <select id="getAssignableCarsByCountry" resultType="com.tyt.plat.vo.other.SigningCarInfoVo">
    select info.id as carInfoId,
           info.driving_ability as drivingAbility,
           info.head_city_no AS headCityNo,
           info.tail_city_no AS tailCityNo,
           info.car_type as carType,
           car.signing as signing,
           car.user_id as userId,
           car.tyt_cell_phone as phone,
           driver.verify_status as verifyStatus,
           info.driver_id as driverId,
           info.driver_user_id as driverUserId,
           info.distance_preference as distancePreference,
           info.driver_tag as driverTag,
           info.favorable_comment AS favorableComment,
           info.receiving_orders AS receivingOrders
    from tyt_signing_car_info info
    left join tyt_invoice_driver driver on info.driver_id = driver.id
    left join tyt_signing_car car on info.signing_id = car.id
    left join tyt_signing_car_info_city city on info.id = city.car_info_id
    where car.status = 1 and city.city = "全国"
    limit #{start}, #{pageSize}
  </select>

  <select id="getSigningCarBlack" resultType="java.lang.Long">
    select DISTINCT info.driver_user_id from tyt_signing_car car
        left join tyt_signing_car_info info on info.signing_id = car.id
                            where car.user_id = #{userId} and info.driver_user_id is not null

  </select>


  <select id="getByUserNoIds" resultType="java.lang.Integer">
    select count(*) from tyt_signing_driver_black where user_id in
    <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
      #{id}
    </foreach>
    and sign_status = 2
  </select>

    <select id="checkDriverBlackStatus" resultType="java.lang.Integer">
        select count(*) from tyt_signing_driver_black where user_id =#{userId} and sign_status = 2
    </select>

</mapper>