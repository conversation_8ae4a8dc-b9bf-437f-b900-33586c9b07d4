<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytUserPermissionGainMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytUserPermissionGain">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="service_permission_type_id" jdbcType="VARCHAR" property="servicePermissionTypeId" />
    <result column="service_permission_type_name" jdbcType="VARCHAR" property="servicePermissionTypeName" />
    <result column="send_type" jdbcType="INTEGER" property="sendType" />
    <result column="gain_type" jdbcType="INTEGER" property="gainType" />
    <result column="buy_goods_id" jdbcType="BIGINT" property="buyGoodsId" />
    <result column="goods_id" jdbcType="BIGINT" property="goodsId" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="expired_time" jdbcType="TIMESTAMP" property="expiredTime" />
    <result column="gain_count" jdbcType="INTEGER" property="gainCount" />
    <result column="use_count" jdbcType="INTEGER" property="useCount" />
    <result column="activity_id" jdbcType="BIGINT" property="activityId" />
    <result column="activity_name" jdbcType="VARCHAR" property="activityName" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>

  <select id="getFirstPermissionGain" resultMap="BaseResultMap">
      select * from tyt_user_permission_gain
      where
        user_id = #{userId}
        and service_permission_type_id = #{servicePermissionTypeId}
        and expired_time &gt;= #{dayStartTime}
        and gain_count &gt; use_count
      order by expired_time asc, id asc
        limit 1
  </select>

  <update id="updatePermissionUseCount">
    update tyt_user_permission_gain
    set use_count = use_count + 1,
        modify_time = now()
    where id = #{gainId}
  </update>

</mapper>