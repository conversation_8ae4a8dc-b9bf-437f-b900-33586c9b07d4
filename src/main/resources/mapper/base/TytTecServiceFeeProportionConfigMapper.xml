<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tyt.plat.mapper.base.TytTecServiceFeeProportionConfigMapper">

    <!-- Result Map 定义 -->
    <resultMap id="TytTecServiceFeeProportionConfigMap" type="com.tyt.plat.entity.base.TytTecServiceFeeProportionConfig">
        <id column="id" property="id"/>
        <result column="config_id" property="configId"/>
        <result column="stage_id" property="stageId"/>
        <result column="tec_service_fee_rate" property="tecServiceFeeRate"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_time" property="modifyTime"/>
    </resultMap>

    <select id="getTytTecServiceFeeProportionConfigByStageId" resultMap="TytTecServiceFeeProportionConfigMap">
        select * from tyt_tec_service_fee_proportion_config where stage_id = #{stageId}
    </select>

</mapper>