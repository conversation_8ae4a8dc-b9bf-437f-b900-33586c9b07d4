<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytCellPhoneChangeAuditMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytCellPhoneChangeAudit">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="true_name" jdbcType="VARCHAR" property="trueName" />
    <result column="old_cell_phone" jdbcType="VARCHAR" property="oldCellPhone" />
    <result column="new_cell_phone" jdbcType="VARCHAR" property="newCellPhone" />
    <result column="hand_id_photo" jdbcType="VARCHAR" property="handIdPhoto" />
    <result column="hand_apply_for_photo" jdbcType="VARCHAR" property="handApplyForPhoto" />
    <result column="audit_status" jdbcType="INTEGER" property="auditStatus" />
    <result column="reject_reason_code" jdbcType="INTEGER" property="rejectReasonCode" />
    <result column="reject_reason_msg" jdbcType="VARCHAR" property="rejectReasonMsg" />
    <result column="opt_id" jdbcType="BIGINT" property="optId" />
    <result column="opt_name" jdbcType="VARCHAR" property="optName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>

  <sql id="BaseSql">
    id,user_id,true_name,old_cell_phone,new_cell_phone,hand_id_photo,hand_apply_for_photo,audit_status,reject_reason_code,reject_reason_msg,opt_id,opt_name
,create_time,modify_time
  </sql>

  <select id="selectByUserIdAndAuditStatus" resultMap="BaseResultMap">
    select
    <include refid="BaseSql"/>
    from tyt_cell_phone_change_audit
    where user_id = #{userId,jdbcType=BIGINT} and audit_status = #{auditStatus,jdbcType=INTEGER} order by id desc limit 1
  </select>
</mapper>