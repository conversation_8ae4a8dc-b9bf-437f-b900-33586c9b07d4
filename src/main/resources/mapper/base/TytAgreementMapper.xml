<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytAgreementMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytAgreement">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="link_url" jdbcType="VARCHAR" property="linkUrl" />
    <result column="show_type" jdbcType="SMALLINT" property="showType" />
    <result column="show_start_time" jdbcType="TIMESTAMP" property="showStartTime" />
    <result column="show_end_time" jdbcType="TIMESTAMP" property="showEndTime" />
    <result column="show_port" jdbcType="SMALLINT" property="showPort" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="status" jdbcType="SMALLINT" property="status" />
    <result column="is_valid" jdbcType="SMALLINT" property="isValid" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="operater" jdbcType="VARCHAR" property="operater" />
    <result column="tab_type" jdbcType="INTEGER" property="tabType" />
  </resultMap>

  <select id="selectAgreementList" resultType="com.tyt.agreement.bean.AgreementListBean">
    SELECT name,link_url as linkUrl
    FROM tyt_agreement
    WHERE tab_type = #{tabType,jdbcType=INTEGER}
      AND is_valid=1
      AND STATUS=2
      AND show_port IN (0,#{showPort,jdbcType=INTEGER})
      AND (show_type=1 OR (show_start_time &lt; NOW()
      AND show_end_time>NOW()))
    ORDER BY sort DESC
  </select>
</mapper>