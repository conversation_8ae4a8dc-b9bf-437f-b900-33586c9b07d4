<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytDispatchCargoOwnerMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytDispatchCargoOwner">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="cell_phone" jdbcType="VARCHAR" property="cellPhone" />
    <result column="owner_name" jdbcType="VARCHAR" property="ownerName" />
    <result column="tel" jdbcType="VARCHAR" property="tel" />
    <result column="owner_type" jdbcType="INTEGER" property="ownerType" />
    <result column="sign_partner" jdbcType="INTEGER" property="signPartner" />
    <result column="assign_car" jdbcType="INTEGER" property="assignCar" />
    <result column="empower_status" jdbcType="INTEGER" property="empowerStatus" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>

  <select id="getByUserId" resultMap="BaseResultMap">
    select * from tyt_dispatch_cargo_owner where user_id = #{userId} and status = 1 order by id desc limit 1
  </select>

  <select id="getSigningList" resultMap="BaseResultMap">
    select user_id,owner_name from tyt_dispatch_cargo_owner where status=1 and sign_partner=1 limit 200
  </select>

  <select id="getByCooperativeId" resultMap="BaseResultMap">
    select * from tyt_dispatch_cargo_owner where cooperative_id = #{cooperativeId} limit 1
  </select>

</mapper>