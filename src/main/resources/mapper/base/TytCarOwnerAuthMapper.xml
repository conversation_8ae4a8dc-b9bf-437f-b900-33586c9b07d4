<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.TytCarOwnerAuthMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.TytCarOwnerAuth">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="car_id" jdbcType="BIGINT" property="carId" />
    <result column="head_no" jdbcType="VARCHAR" property="headNo" />
    <result column="head_name" jdbcType="VARCHAR" property="headName" />
    <result column="car_user_phone" jdbcType="VARCHAR" property="carUserPhone" />
    <result column="car_identity" jdbcType="INTEGER" property="carIdentity" />
    <result column="car_relation" jdbcType="INTEGER" property="carRelation" />
    <result column="car_owner_id_number" jdbcType="VARCHAR" property="carOwnerIdNumber" />
    <result column="id_photo" jdbcType="VARCHAR" property="idPhoto" />
    <result column="auth_materials_type" jdbcType="INTEGER" property="authMaterialsType" />
<!--    <result column="certificate_material" jdbcType="VARCHAR" property="certificateMaterial" />-->
    <result column="auth_status" jdbcType="INTEGER" property="authStatus" />
    <result column="auth_user_id" jdbcType="INTEGER" property="authUserId" />
    <result column="auth_user_name" jdbcType="VARCHAR" property="authUserName" />
    <result column="auth_time" jdbcType="TIMESTAMP" property="authTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
  </resultMap>

  <sql id="BaseResultSql">
    id,user_id,car_id,head_no,head_name,car_user_phone,car_identity,car_relation,car_owner_id_number,id_photo,auth_materials_type,auth_status,auth_user_id,auth_user_name,auth_time,modify_time,create_time,is_delete
  </sql>

  <select id="selectByUserIdAuthStatus" resultMap="BaseResultMap">
    select
    <include refid="BaseResultSql"/>
    from tyt_car_owner_auth
    where user_id = #{userId,jdbcType=BIGINT} and auth_status = #{authStatus,jdbcType=INTEGER}
  </select>

  <select id="countSucceedNum" resultType="int">
    select count(*) from tyt_car_owner_auth where user_id = #{userId,jdbcType=BIGINT} and auth_status = 1 and is_delete = 1
  </select>

  <select id="selectByCarId" resultMap="BaseResultMap">
    select
    <include refid="BaseResultSql"/>
    from tyt_car_owner_auth
    where car_id = #{carId,jdbcType=BIGINT} and is_delete = 1 limit 1
  </select>

  <select id="selectConvertMap" resultMap="BaseResultMap">
    select * from tyt_car_owner_auth where user_id = #{userId,jdbcType=BIGINT} and auth_status = 1 and is_delete = 1
  </select>

  <select id="selectCarIds" resultType="java.lang.Long">
    select car_id from tyt_car_owner_auth where user_id = #{userId,jdbcType=BIGINT} and auth_status = 1 and is_delete = 1 group by car_id
  </select>

  <select id="selectTytCarIds" resultType="java.lang.Long">
    SELECT id FROM tyt_car c WHERE  c.user_id=#{userId,jdbcType=BIGINT} and c.auth=#{auth,jdbcType=INTEGER}  and is_delete=1
  </select>

  <select id="selectByCarIdAndAuth" resultMap="BaseResultMap">
    select
    <include refid="BaseResultSql"/>
    from tyt_car_owner_auth
    where car_id = #{carId,jdbcType=BIGINT} and is_delete = 1 and auth_status in (0,1) limit 1
  </select>
</mapper>