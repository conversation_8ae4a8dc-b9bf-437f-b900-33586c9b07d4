<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.FeedbackUserOverviewMapper">
  <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.FeedbackUserOverview">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="user_type" jdbcType="SMALLINT" property="userType" />
    <result column="posted_positive_feedback_num" jdbcType="INTEGER" property="postedPositiveFeedbackNum" />
    <result column="posted_neutral_feedback_num" jdbcType="INTEGER" property="postedNeutralFeedbackNum" />
    <result column="posted_negative_feedback_num" jdbcType="INTEGER" property="postedNegativeFeedbackNum" />
    <result column="posted_feedback_num" jdbcType="INTEGER" property="postedFeedbackNum" />
    <result column="received_positive_feedback_num" jdbcType="INTEGER" property="receivedPositiveFeedbackNum" />
    <result column="received_neutral_feedback_num" jdbcType="INTEGER" property="receivedNeutralFeedbackNum" />
    <result column="received_negative_feedback_num" jdbcType="INTEGER" property="receivedNegativeFeedbackNum" />
    <result column="received_feedback_num" jdbcType="INTEGER" property="receivedFeedbackNum" />
    <result column="received_positive_feedback_rating" jdbcType="INTEGER" property="receivedPositiveFeedbackRating" />
  </resultMap>
</mapper>