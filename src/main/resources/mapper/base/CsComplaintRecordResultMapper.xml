<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.plat.mapper.base.CsComplaintRecordResultMapper">
    <resultMap id="BaseResultMap" type="com.tyt.plat.entity.base.CsComplaintRecordResult">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="record_id" jdbcType="BIGINT" property="recordId"/>
        <result column="is_valid_complaint" jdbcType="INTEGER" property="isValidComplaint"/>
        <result column="responsible_party" jdbcType="INTEGER" property="responsibleParty"/>
        <result column="wrong_reason" jdbcType="INTEGER" property="wrongReason"/>
        <result column="wrong_remark" jdbcType="VARCHAR" property="wrongRemark"/>
        <result column="handle_standard" jdbcType="INTEGER" property="handleStandard"/>
        <result column="is_distribution" jdbcType="INTEGER" property="isDistribution"/>
        <result column="is_compensation" jdbcType="INTEGER" property="isCompensation"/>
        <result column="is_compensate" jdbcType="INTEGER" property="isCompensate"/>
        <result column="compensate_type" jdbcType="INTEGER" property="compensateType"/>
        <result column="complaint_qus_one" jdbcType="BIGINT" property="complaintQusOne"/>
        <result column="complaint_qus_two" jdbcType="BIGINT" property="complaintQusTwo"/>
        <result column="complaint_qus_three" jdbcType="BIGINT" property="complaintQusThree"/>
        <result column="complaint_qus_four" jdbcType="BIGINT" property="complaintQusFour"/>
        <result column="ctime" jdbcType="TIMESTAMP" property="ctime"/>
        <result column="op_name" jdbcType="VARCHAR" property="opName"/>
        <result column="responsible_party_violation" jdbcType="INTEGER" property="responsiblePartyViolation"/>
        <result column="is_distribution_two" jdbcType="INTEGER" property="isDistributionTwo"/>
        <result column="responsible_party_two" jdbcType="INTEGER" property="responsiblePartyTwo"/>
    </resultMap>

    <select id="selectCountByReason" resultType="java.lang.Integer">
        select count(1)
        from tyt.cs_complaint_record_result
        where wrong_reason = 2962
          and is_valid_complaint = 2
          and responsible_party_violation = 3
          and record_id in (select id
                            from tyt.cs_complaint_record
                            where pass_complaint_auth = 1
                              and pass_complaint_num = #{cellPhone}
                              and work_order_status = 7
                              and finish_time > date_sub(curdate(), interval 30 day));
    </select>


</mapper>