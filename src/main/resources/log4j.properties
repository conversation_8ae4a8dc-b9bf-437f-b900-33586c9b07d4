log4j.rootLogger=info,console
log4j.appender.console=org.apache.log4j.ConsoleAppender
log4j.appender.console.layout=org.apache.log4j.PatternLayout
#log4j.appender.console.layout=org.apache.log4j.TTCCLayout
log4j.appender.console.layout.ConversionPattern=%d{yyyy-MM-dd HH\:mm\:ss,SSS} %t [%X{TRACE_ID}] %5p %c\:(%F\:%L) - %m%n
#log4j.logger.com.tyt.web.TransportController=debug,console
#log4j.logger.com.tyt.web.BaseController=debug,console
#log4j.logger.com.tyt.cache=debug,console

#log4j.logger.com.tyt=info,external
#log4j.appender.external=org.apache.log4j.DailyRollingFileAppender
#log4j.appender.external.DatePattern='.'yyyy-MM-dd
#log4j.appender.external.File=/data/logs/plat.log
#log4j.appender.external.layout=org.apache.log4j.PatternLayout
#log4j.appender.external.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss} %5p %c\:(%F\:%L)-%m%n

###logTime requestPath, clientSign,osVersion, clientVersion, clientId,sign, userId, requesParameters
log4j.logger.com.tyt.util.FilterLog=info,requestLog
log4j.additivity.com.tyt.util.FilterLog=false
log4j.appender.requestLog=org.apache.log4j.DailyRollingFileAppender
log4j.appender.requestLog.DatePattern='-'yyyyMMddHH'.log'
log4j.appender.requestLog.File=/data/logs/plat/requestLog.log
log4j.appender.requestLog.layout=org.apache.log4j.PatternLayout
log4j.appender.requestLog.layout.ConversionPattern=[%X{TRACE_ID}] %d{yyyy-MM-dd HH:mm:ss}%m%n
log4j.appender.requestLog.BufferSize=8192
log4j.appender.requestLog.BufferedIO=true

log4j.logger.detailsLog=info,detailsLog
log4j.additivity.detailsLog=false
log4j.appender.detailsLog=com.tyt.util.TytJDBCAppender
log4j.appender.detailsLog.BufferSize=3
log4j.appender.detailsLog.sql=INSERT INTO tyt_log_ts_details (user_id, ts_id, client_version, client_sign, STATUS, ctime, view_source, sort_type, sort_index, special_mark, start_provinc, start_city, start_area, dest_provinc, dest_city, dest_area, benefit_label_code, sharer_user_id)VALUES(%m);
log4j.appender.detailsLog.layout=org.apache.log4j.PatternLayout

log4j.logger.searchLog=info,searchLog
log4j.additivity.searchLog= false
log4j.appender.searchLog=com.tyt.util.TytJDBCAppender
log4j.appender.searchLog.BufferSize=3
log4j.appender.searchLog.sql=INSERT INTO tyt_log_ts_search (user_id, start_coord, start_range, dest_coord, dest_range, car_id, head_no, head_city, client_version, client_sign, sort_type, ctime,number_type,os_version,client_id,car_length,car_type,special_required,start_weight,end_weight) VALUES (%m);
log4j.appender.searchLog.layout=org.apache.log4j.PatternLayout

log4j.logger.distanceSortLog=info,distanceSortLog
log4j.additivity.distanceSortLog= false
log4j.appender.distanceSortLog=com.tyt.util.TytJDBCAppender
log4j.appender.distanceSortLog.BufferSize=3
log4j.appender.distanceSortLog.sql=INSERT INTO tyt_log_ts_search_d (user_id, create_time, client_version,client_sign, sort_type) VALUES (%X{userId},'%X{dateStr}','%X{clientVersion}',%X{clientSign},%X{sortType}%m);
log4j.appender.distanceSortLog.layout=org.apache.log4j.PatternLayout

log4j.logger.searchFallShortLog=info,searchFallShortLog
log4j.additivity.searchFallShortLog= false
log4j.appender.searchFallShortLog=com.tyt.util.TytJDBCAppender
log4j.appender.searchFallShortLog.BufferSize=3
log4j.appender.searchFallShortLog.sql=INSERT INTO tyt_log_fall_short_search (user_id, start_coord, start_distance, start_provinc, start_city, start_area, client_version, client_sign, number_type,os_version,client_id,search_type,ctime) VALUES (%m);
log4j.appender.searchFallShortLog.layout=org.apache.log4j.PatternLayout

log4j.logger.gatherLog=error,gatherLog
log4j.additivity.gatherLog= false
log4j.appender.gatherLog=org.apache.log4j.DailyRollingFileAppender
log4j.appender.gatherLog.DatePattern='_'yyyyMMdd'.log'
log4j.appender.gatherLog.File=/data/logs/plat/gatherLog.log
log4j.appender.gatherLog.layout=org.apache.log4j.PatternLayout
log4j.appender.gatherLog.layout.ConversionPattern=[%X{TRACE_ID}] %d{yyyy-MM-dd HH:mm:ss} %5p %c\:(%F\:%L)-%m%n
log4j.appender.gatherLog.BufferSize=8192
log4j.appender.gatherLog.BufferedIO=true

log4j.logger.ipLog=info,ipLog
log4j.appender.ipLog=org.apache.log4j.DailyRollingFileAppender
log4j.appender.ipLog.DatePattern='_'yyyyMMdd'.log'
log4j.appender.ipLog.File=/data/logs/plat/ip.log
log4j.appender.ipLog.layout=org.apache.log4j.PatternLayout
log4j.appender.ipLog.layout.ConversionPattern=[%X{TRACE_ID}] %d{yyyy-MM-dd HH:mm:ss} %5p:(%F\:%L)-%m%n
log4j.appender.ipLog.BufferSize=8192
log4j.appender.ipLog.BufferedIO=true


log4j.logger.htbx=info,htbx
log4j.appender.htbx=org.apache.log4j.DailyRollingFileAppender
log4j.appender.htbx.DatePattern='_'yyyyMMdd'.log'
log4j.appender.htbx.File=/data/logs/plat/htbx.log
log4j.appender.htbx.layout=org.apache.log4j.PatternLayout
log4j.appender.htbx.layout.ConversionPattern=[%X{TRACE_ID}] %d{yyyy-MM-dd HH:mm:ss} %5p:(%F\:%L)-%m%n
log4j.appender.htbx.BufferSize=8192
log4j.appender.htbx.BufferedIO=true


log4j.logger.htbxpay=info,htbxpay
log4j.appender.htbxpay=org.apache.log4j.DailyRollingFileAppender
log4j.appender.htbxpay.DatePattern='_'yyyyMMdd'.log'
log4j.appender.htbxpay.File=/data/logs/plat/pay.log
log4j.appender.htbxpay.layout=org.apache.log4j.PatternLayout
log4j.appender.htbxpay.layout.ConversionPattern=[%X{TRACE_ID}] %d{yyyy-MM-dd HH:mm:ss} %5p:(%F\:%L)-%m%n
log4j.appender.htbxpay.BufferSize=8192
log4j.appender.htbxpay.BufferedIO=true


log4j.logger.userApp=info,userApp
log4j.appender.userApp=org.apache.log4j.DailyRollingFileAppender
log4j.appender.userApp.DatePattern='_'yyyyMMdd'.log'
log4j.appender.userApp.File=/data/logs/plat/userApp.log
log4j.appender.userApp.layout=org.apache.log4j.PatternLayout
log4j.appender.userApp.layout.ConversionPattern=[%X{TRACE_ID}] %d{yyyy-MM-dd HH:mm:ss}%m%n
log4j.appender.userApp.BufferSize=8192
log4j.appender.userApp.BufferedIO=true


log4j.logger.apiLog=info,apiLog
log4j.appender.apiLog=org.apache.log4j.DailyRollingFileAppender
log4j.appender.apiLog.DatePattern='-'yyyyMMdd'.log'
log4j.appender.apiLog.File=/data/logs/apiLog_app.log
log4j.appender.apiLog.layout=org.apache.log4j.PatternLayout
log4j.appender.apiLog.layout.ConversionPattern=[%X{TRACE_ID}] %d{yyyy-MM-dd HH:mm:ss}%m%n
log4j.appender.apiLog.BufferSize=8192
log4j.appender.apiLog.BufferedIO=true
log4j.additivity.apiLog=false

#log4j.appender.console.Threshold=trace
log4j.logger.org.springframework=error
log4j.logger.org.hibernate=error
### log JDBC bind parameters ###
#log4j.logger.org.hibernate.type=trace
### log schema export/update ###
log4j.logger.org.hibernate.tool.hbm2ddl=error
### log JDBC bind transaction ###
log4j.logger.org.hibernate.transaction=error

log4j.logger.org.jasig.cas.client.util.CommonUtils=error


# SQL statements and parameters
#log4j.logger.org.hibernate.SQL=debug
#log4j.logger.org.hibernate.type.descriptor.sql=trace

log4j.logger.com.tyt.plat.mapper=debug,console
log4j.logger.com.tyt.plat.commons.tools=debug,console

log4j.com.tyt.plat.mapper=debug,console
log4j.com.tyt.plat.commons.tools=debug,console
