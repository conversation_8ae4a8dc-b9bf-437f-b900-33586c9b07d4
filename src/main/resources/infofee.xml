<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE generatorConfiguration PUBLIC
        "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd" >
<generatorConfiguration>
    <classPathEntry location="/Users/<USER>/.m2/repository/mysql/mysql-connector-java/5.0.8/mysql-connector-java-5.0.8.jar"></classPathEntry>

    <context id="context">
        <commentGenerator>
            <property name="suppressAllComments" value="true"/>
            <property name="suppressDate" value="true"/>
        </commentGenerator>

        <jdbcConnection userId="root" password="2817e51697" driverClass="com.mysql.jdbc.Driver" connectionURL="***********************************"/>

        <javaTypeResolver>
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>

        <javaModelGenerator targetPackage="com.tyt.infofee.bean" targetProject=".">
            <property name="enableSubPackages" value="false"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>

        <sqlMapGenerator targetPackage="com.tyt.mybatis.mapper" targetProject=".">
            <property name="enableSubPackages" value="false"/>
        </sqlMapGenerator>
        <javaClientGenerator targetPackage="com.tyt.mybatis.mapper"
                             targetProject="." type="XMLMAPPER">
            <property name="enableSubPackages" value="false" />
        </javaClientGenerator>

        <!--<table tableName="tyt_transport_orders_log" enableCountByExample="false"-->
               <!--enableDeleteByExample="false" enableDeleteByPrimaryKey="false" enableSelectByPrimaryKey="false"-->
               <!--enableSelectByExample="false" />-->
        
        <!-- 用户钱包 -->
        <table tableName="tyt_user_account"              enableCountByExample="false"
               enableDeleteByExample="false" enableDeleteByPrimaryKey="false" enableSelectByPrimaryKey="true"
               enableSelectByExample="false" />

        <!--&lt;!&ndash; 出入帐 &ndash;&gt;-->
        <!--<table tableName="tyt_financial_in_out_account"  enableCountByExample="false"-->
               <!--enableDeleteByExample="false" enableDeleteByPrimaryKey="false" enableSelectByPrimaryKey="false"-->
               <!--enableSelectByExample="false"/>-->

        <!--&lt;!&ndash; 流水 &ndash;&gt;-->
        <!--<table tableName="tyt_financial_flow"  enableCountByExample="false"-->
               <!--enableDeleteByExample="false" enableDeleteByPrimaryKey="false" enableSelectByPrimaryKey="false"-->
               <!--enableSelectByExample="false"/>-->
    </context>
</generatorConfiguration>