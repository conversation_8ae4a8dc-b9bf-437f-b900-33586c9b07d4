{"properties": {"id": {"type": "long"}, "startPoint": {"type": "text", "fields": {"keyword": {"ignore_above": 256, "type": "keyword"}}}, "destPoint": {"type": "text", "fields": {"keyword": {"ignore_above": 256, "type": "keyword"}}}, "taskContent": {"type": "text", "fields": {"keyword": {"ignore_above": 501, "type": "keyword"}}}, "tel": {"type": "keyword"}, "pubTime": {"type": "keyword"}, "pubQq": {"type": "long"}, "nickName": {"type": "keyword"}, "status": {"type": "integer"}, "source": {"type": "integer"}, "ctime": {"type": "date"}, "mtime": {"type": "date"}, "uploadCellphone": {"type": "keyword"}, "resend": {"type": "integer"}, "startCoord": {"type": "keyword"}, "destCoord": {"type": "keyword"}, "platId": {"type": "integer"}, "verifyFlag": {"type": "integer"}, "price": {"type": "keyword"}, "userId": {"type": "long"}, "priceCode": {"type": "keyword"}, "startCoordX": {"type": "integer"}, "startCoordY": {"type": "integer"}, "destCoordX": {"type": "integer"}, "destCoordY": {"type": "integer"}, "startDetailAdd": {"type": "text", "fields": {"keyword": {"ignore_above": 256, "type": "keyword"}}}, "startLongitude": {"type": "integer"}, "startLatitude": {"type": "integer"}, "destDetailAdd": {"type": "text", "fields": {"keyword": {"ignore_above": 256, "type": "keyword"}}}, "destLongitude": {"type": "integer"}, "destLatitude": {"type": "integer"}, "pubDate": {"type": "date"}, "goodsCode": {"type": "keyword"}, "weightCode": {"type": "keyword"}, "weight": {"type": "keyword"}, "length": {"type": "keyword"}, "wide": {"type": "keyword"}, "high": {"type": "keyword"}, "isSuperelevation": {"type": "integer"}, "linkman": {"type": "keyword"}, "remark": {"type": "text", "fields": {"keyword": {"ignore_above": 501, "type": "keyword"}}}, "distance": {"type": "integer"}, "pubGoodsTime": {"type": "date"}, "tel3": {"type": "keyword"}, "tel4": {"type": "keyword"}, "displayType": {"type": "keyword"}, "hashCode": {"type": "keyword"}, "isCar": {"type": "keyword"}, "userType": {"type": "integer"}, "pcOldContent": {"type": "text", "fields": {"keyword": {"ignore_above": 256, "type": "keyword"}}}, "resendCounts": {"type": "integer"}, "verifyPhotoSign": {"type": "integer"}, "userPart": {"type": "integer"}, "startCity": {"type": "keyword"}, "startProvinc": {"type": "keyword"}, "startArea": {"type": "keyword"}, "srcMsgId": {"type": "long"}, "destProvinc": {"type": "keyword"}, "destCity": {"type": "keyword"}, "destArea": {"type": "keyword"}, "clientVersion": {"type": "keyword"}, "isInfoFee": {"type": "keyword"}, "infoStatus": {"type": "keyword"}, "tsOrderNo": {"type": "keyword"}, "releaseTime": {"type": "date"}, "regTime": {"type": "date"}, "type": {"type": "keyword"}, "brand": {"type": "keyword"}, "goodTypeName": {"type": "keyword"}, "goodNumber": {"type": "integer"}, "isStandard": {"type": "integer"}, "matchItemId": {"type": "integer"}, "androidDistance": {"type": "integer"}, "iosDistance": {"type": "integer"}, "isDisplay": {"type": "integer"}, "referLength": {"type": "integer"}, "referWidth": {"type": "integer"}, "referHeight": {"type": "integer"}, "referWeight": {"type": "integer"}, "startCoordXValue": {"type": "keyword"}, "startCoordYValue": {"type": "keyword"}, "destCoordXValue": {"type": "keyword"}, "destCoordYValue": {"type": "keyword"}, "startLatitudeValue": {"type": "keyword"}, "startLongitudeValue": {"type": "keyword"}, "destLatitudeValue": {"type": "keyword"}, "destLongitudeValue": {"type": "keyword"}, "distanceValue": {"type": "keyword"}, "changeTime": {"type": "date"}, "carLength": {"type": "keyword"}, "carType": {"type": "keyword"}, "specialRequired": {"type": "keyword"}, "similarityCode": {"type": "keyword"}, "similarityFirstId": {"type": "long"}, "similarityFirstInfo": {"type": "keyword"}, "loadingTime": {"type": "date"}, "unloadTime": {"type": "date"}, "carMinLength": {"type": "float"}, "carMaxLength": {"type": "float"}, "carStyle": {"type": "keyword"}, "workPlaneMinHigh": {"type": "float"}, "workPlaneMaxHigh": {"type": "float"}, "workPlaneMinLength": {"type": "float"}, "workPlaneMaxLength": {"type": "float"}, "climb": {"type": "keyword"}}}