<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:tx="http://www.springframework.org/schema/tx" xmlns:context="http://www.springframework.org/schema/context"
       xmlns:task="http://www.springframework.org/schema/task" xmlns:mybatis="http://mybatis.org/schema/mybatis-spring"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
          http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
          http://www.springframework.org/schema/tx
          http://www.springframework.org/schema/tx/spring-tx-4.0.xsd
          http://www.springframework.org/schema/context
          http://www.springframework.org/schema/context/spring-context-4.0.xsd
          http://www.springframework.org/schema/aop
          http://www.springframework.org/schema/aop/spring-aop-4.0.xsd
          http://www.springframework.org/schema/task
          http://www.springframework.org/schema/task/spring-task-4.0.xsd http://mybatis.org/schema/mybatis-spring http://mybatis.org/schema/mybatis-spring.xsd"
       default-autowire="byName">

    <bean id="applicationContextUtils"
          class="com.tyt.util.ApplicationContextUtils" />

    <!-- 注解扫描包 -->
    <context:component-scan base-package="com.tyt">
        <context:exclude-filter type="annotation"
                                expression="org.springframework.stereotype.Controller" />
    </context:component-scan>


    <!-- 上传文件 -->
    <bean id="multipartResolver"
          class="org.springframework.web.multipart.commons.CommonsMultipartResolver">
        <property name="defaultEncoding" value="UTF-8"></property>
        <property name="maxUploadSize" value="10485760" />
    </bean>

    <context:property-placeholder location="classpath:server_url.properties"/>
    <!-- 配置数据源 -->
    <!-- 本地环境配置 -->
    <bean id="dataSource"
          class="com.mchange.v2.c3p0.ComboPooledDataSource" destroy-method="close">
        <property name="driverClass" value="${jdbc_driverClassName}" />
        <property name="jdbcUrl"
                  value="${tyt_jdbc_url}"
        />
        <property name="user"               value="${tyt_jdbc_username}" />
        <property name="password"
                  value="${tyt_jdbc_password}" />
        <property name="maxPoolSize"        value="${tyt_jdbc_maxPoolSize}" />
        <property name="minPoolSize"        value="${tyt_jdbc_minPoolSize}" />
        <property name="maxStatements"      value="${tyt_jdbc_maxStatements}" />
        <property name="initialPoolSize"    value="${tyt_jdbc_initialPoolSize}" />
        <property name="maxIdleTime"        value="${tyt_jdbc_maxIdleTime}"/>
        <property name="idleConnectionTestPeriod"   value="${tyt_jdbc_idleConnectionTestPeriod}" />
        <property name="testConnectionOnCheckin"    value="true" />
        <property name="testConnectionOnCheckout"   value="false" />
        <property name="preferredTestQuery"         value="SELECT 1
FROM DUAL" />
    </bean>
    <!-- 测试环境配置 -->
    <!-- <bean id="dataSource"
           class="com.mchange.v2.c3p0.ComboPooledDataSource" destroy-method="close">
         <property name="driverClass" value="com.mysql.jdbc.Driver" />
         <property name="jdbcUrl"
                   value="***************************************************************************"
                 />
         <property name="user"               value="root" />
         <property name="password"
                   value="2817e51697" />
         <property name="maxPoolSize"        value="120" />
         <property name="minPoolSize"        value="20" />
         <property name="maxStatements"      value="100" />
         <property name="initialPoolSize"    value="10" />
         <property name="maxIdleTime"        value="60"/>
         <property name="idleConnectionTestPeriod"   value="10" />
         <property name="testConnectionOnCheckin"    value="true" />
         <property name="testConnectionOnCheckout"   value="false" />
         <property name="preferredTestQuery"         value="SELECT 1
 FROM DUAL" />
     </bean> -->
    <!-- 生产环境配置 -->
    <!-- <bean id="dataSource"
              class="com.mchange.v2.c3p0.ComboPooledDataSource" destroy-method="close">
            <property name="driverClass" value="com.mysql.jdbc.Driver" />
            <property name="jdbcUrl"
                      value="*************************************************************************************************"

                    />
            <property name="user"               value="tyt_write" />
            <property name="password"
                      value="tyt_write" />
            <property name="maxPoolSize"        value="120" />
            <property name="minPoolSize"        value="20" />
            <property name="maxStatements"      value="100" />
            <property name="initialPoolSize"    value="10" />
            <property name="maxIdleTime"        value="60"/>
            <property name="idleConnectionTestPeriod"   value="10" />
            <property name="testConnectionOnCheckin"    value="true" />
            <property name="testConnectionOnCheckout"   value="false" />
            <property name="preferredTestQuery"         value="SELECT 1
    FROM DUAL" />
        </bean>  -->
    <!-- 最大空闲时间,60秒内未使用则连接被丢弃。若为0则永不丢弃。Default: 0   -->

    <!-- 配置SessionFactory -->
    <bean id="sessionFactory"
          class="org.springframework.orm.hibernate3.annotation.AnnotationSessionFactoryBean">
        <property name="dataSource" ref="dataSource" />
        <property name="packagesToScan">
            <list><!-- 这里直接映射的pojo类所在的包,简单方便不用没次加一个pojo类都需要到这里来添加 -->
                <value>com.tyt.model</value>
            </list>
        </property>
        <property name="hibernateProperties">
            <props>
                <prop key="hibernate.dialect">org.hibernate.dialect.MySQLDialect</prop>
                <prop key="hibernate.show_sql">false</prop>
                <prop key="hibernate.format_sql">false</prop>
                <prop key="hibernate.connection.autocommit">true</prop>
            </props>
        </property>
    </bean>

    <!--<bean id="readSessionFactory"
          class="org.springframework.orm.hibernate3.annotation.AnnotationSessionFactoryBean">
        <property name="dataSource" ref="readDataSource" />
        <property name="packagesToScan">
            <list>&lt;!&ndash; 这里直接映射的pojo类所在的包,简单方便不用没次加一个pojo类都需要到这里来添加 &ndash;&gt;
                <value>com.tyt.model</value>
            </list>
        </property>
        <property name="hibernateProperties">
            <props>
                <prop key="hibernate.dialect">org.hibernate.dialect.MySQLDialect</prop>
                <prop key="hibernate.show_sql">true</prop>
                <prop key="hibernate.format_sql">true</prop>
                <prop key="hibernate.connection.autocommit">true</prop>
            </props>
        </property>
    </bean>
-->
    <!-- HibernateTemplate -->
    <bean id="hibernateTemplate" class="org.springframework.orm.hibernate3.HibernateTemplate" lazy-init="true">
        <property name="sessionFactory" ref="sessionFactory" />
        <property name="cacheQueries" value="true" />
    </bean>


    <!--<bean id="readHibernateTemplate" class="org.springframework.orm.hibernate3.HibernateTemplate" lazy-init="true">
        <property name="sessionFactory" ref="readSessionFactory" />
        <property name="cacheQueries" value="true" />
    </bean>-->

    <!-- 配置一个事务管理器 -->
    <bean id="transactionManager" class="org.springframework.orm.hibernate3.HibernateTransactionManager">
        <property name="sessionFactory" ref="sessionFactory"/>
    </bean>

    <!-- 配置事务，使用代理的方式 -->
    <!--
	<bean id="transactionProxy" class="org.springframework.transaction.interceptor.TransactionProxyFactoryBean" abstract="true">
	    <property name="transactionManager" ref="transactionManager"></property>
	    <property name="transactionAttributes">
	        <props>
	            <prop key="add*">PROPAGATION_REQUIRED,-Exception</prop>
	            <prop key="modify*">PROPAGATION_REQUIRED,-myException</prop>
	            <prop key="del*">PROPAGATION_REQUIRED</prop>
	            <prop key="*">PROPAGATION_REQUIRED</prop>
	        </props>
	    </property>
	</bean>             -->

    <tx:advice id="txAdvice" transaction-manager="transactionManager">
        <tx:attributes>
            <tx:method name="updateGetNextSequenceNbr" propagation="REQUIRES_NEW" rollback-for="Exception"/>
            <tx:method name="updateGetNumberForDate" propagation="REQUIRES_NEW" rollback-for="Exception"/>
            <tx:method name="updateGetOrderNumberForDateTime" propagation="REQUIRES_NEW" rollback-for="Exception"/>
            <tx:method name="userAgreeAffix" propagation="REQUIRED" rollback-for="Exception" />
            <tx:method name="updateGetNumberForDateTime" propagation="REQUIRES_NEW" rollback-for="Exception"/>
            <tx:method name="userDoAffix" propagation="REQUIRED" rollback-for="Exception" />
            <tx:method name="save*" propagation="REQUIRED" rollback-for="Exception"/>
            <tx:method name="send*" propagation="REQUIRED" rollback-for="Exception"/>
            <tx:method name="add*" propagation="REQUIRED" rollback-for="Exception"/>
            <tx:method name="insert*" propagation="REQUIRED" rollback-for="Exception"/>
            <tx:method name="create*" propagation="REQUIRED" rollback-for="Exception"/>
            <tx:method name="update*" propagation="REQUIRED" rollback-for="Exception"/>
            <tx:method name="del*" propagation="REQUIRED" rollback-for="Exception"/>
            <tx:method name="remove*" propagation="REQUIRED" rollback-for="Exception"/>
            <tx:method name="clear*" propagation="REQUIRED" rollback-for="Exception"/>
            <tx:method name="disabled*" propagation="REQUIRED" rollback-for="Exception" />
            <tx:method name="send" propagation="REQUIRED" rollback-for="Exception" />
            <tx:method name="verify" propagation="REQUIRED" rollback-for="Exception" />
            <tx:method name="getPhoneByGoodId" propagation="REQUIRED" rollback-for="Exception" />
            <tx:method name="getPhoneByGoodIdWithRights" propagation="REQUIRED" rollback-for="Exception" />
            <tx:method name="queryMachineTypeByKeyword" propagation="REQUIRED" rollback-for="Exception" />
            <tx:method name="queryMachineTypeGroupByKeyword" propagation="REQUIRED" rollback-for="Exception" />
            <tx:method name="getMergeOrdersList" propagation="REQUIRED" rollback-for="Exception" />
            <tx:method name="getPhoneByGoodIdNew" propagation="REQUIRED" rollback-for="Exception" />
            <tx:method name="query*" propagation="REQUIRED" read-only="true"/>
            <tx:method name="get*" propagation="REQUIRED" read-only="true" />
            <tx:method name="isExist*" propagation="REQUIRED" read-only="true" />
            <tx:method name="query*" propagation="REQUIRED" read-only="true" />
            <tx:method name="is*" propagation="REQUIRED" read-only="true" />
            <tx:method name="read*" propagation="REQUIRED" read-only="true" />
            <tx:method name="find*" propagation="REQUIRED" read-only="true" />
            <tx:method name="count*" propagation="REQUIRED" read-only="true" />
            <!-- 			<tx:method name="*" read-only="true" /> -->
        </tx:attributes>
    </tx:advice>
    <!-- -
        <aop:config expose-proxy="true">
            <aop:advisor pointcut="execution(public * com.tyt.service.*.*.*(..))"
                         advice-ref="txAdvice" />
        </aop:config>

        -->
    <aop:config expose-proxy="true">
        <aop:advisor pointcut="execution(public * com.tyt.*.service.*.*(..))"
                     advice-ref="txAdvice" />
    </aop:config>

    <!-- tx:annotation 自动配置事务，注意这个标签必需放在tx:advice下面，否则不起作用 -->
    <tx:annotation-driven/>

    <task:annotation-driven/>

    <!-- 处理mq的线程池 -->
    <task:executor id="mqExecutor" pool-size="15-300" queue-capacity="5" keep-alive="5"/>

    <!-- mybatis -->

    <bean id="mybatisDataSource" class="com.alibaba.druid.pool.DruidDataSource" destroy-method="close">
        <property name="url" value="${tyt_jdbc_url}"/>
        <property name="username" value="${tyt_jdbc_username}"/>
        <property name="password" value="${tyt_jdbc_password}"/>

        <property name="initialSize" value="10"/>
        <property name="minIdle" value="10"/>
        <property name="maxActive" value="100"/>

        <property name="maxWait" value="1000"/>

        <property name="timeBetweenEvictionRunsMillis" value="60000"/>

        <property name="minEvictableIdleTimeMillis" value="300000"/>

        <property name="testWhileIdle" value="true"/>
        <property name="testOnBorrow" value="false"/>
        <property name="testOnReturn" value="false"/>
        <property name="validationQuery" value="SELECT 1"/>
        <property name="poolPreparedStatements" value="true"/>
        <property name="maxPoolPreparedStatementPerConnectionSize" value="20"/>

        <property name="filters" value="stat"/>
    </bean>

    <bean id="mybatisSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="mybatisDataSource" />
        <!-- 引入映射文件 -->
        <property name="mapperLocations" value="classpath:mapper/base/*.xml" />
        <!-- 注意其他配置 -->
        <property name="plugins">
            <array>
                <bean class="com.github.pagehelper.PageInterceptor"/>
            </array>
        </property>
    </bean>

    <bean id="mybatisTransactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="mybatisDataSource" />
    </bean>

    <!-- 只有再该包下才使用当前数据源 -->
    <bean id="baseMapperScanner" class="tk.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.tyt.mybatis.mapper,com.tyt.plat.mapper.base,com.tyt.deposit.mapper.base"/>
        <property name="sqlSessionFactoryBeanName" value="mybatisSqlSessionFactory" />
    </bean>

<!--    <mybatis:scan base-package="com.tyt.mybatis.mapper" />-->

</beans>
