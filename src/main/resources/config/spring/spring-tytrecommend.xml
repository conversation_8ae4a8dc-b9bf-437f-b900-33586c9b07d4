<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:tx="http://www.springframework.org/schema/tx" xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="
          http://www.springframework.org/schema/beans
          http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
          http://www.springframework.org/schema/tx
          http://www.springframework.org/schema/tx/spring-tx-4.0.xsd
          http://www.springframework.org/schema/context
          http://www.springframework.org/schema/context/spring-context-4.0.xsd
          http://www.springframework.org/schema/aop
          http://www.springframework.org/schema/aop/spring-aop-4.0.xsd"
       default-autowire="byName">
    <!-- 注解扫描包 -->
    <context:component-scan base-package="com.tytrecommend" use-default-filters="true">
        <context:exclude-filter type="annotation"
                                expression="org.springframework.stereotype.Controller"/>
    </context:component-scan>
    <!-- 配置数据源 -->
    <!-- 本地共用服务器 -->
    <bean id="dataSourceTytRecommend" class="com.mchange.v2.c3p0.ComboPooledDataSource" destroy-method="close">
        <property name="driverClass" value="${jdbc_driverClassName}"/>
        <property name="jdbcUrl" value="${tytrecommend_jdbc_url}"/>
        <property name="user" value="${tytrecommend_jdbc_username}"/>
        <property name="password" value="${tytrecommend_jdbc_password}"/>
        <property name="maxPoolSize" value="${tytrecommend_jdbc_maxPoolSize}"/>
        <property name="minPoolSize" value="${tytrecommend_jdbc_minPoolSize}"/>
        <property name="maxStatements" value="${tytrecommend_jdbc_maxStatements}"/>
        <property name="initialPoolSize" value="${tytrecommend_jdbc_initialPoolSize}"/>
        <property name="maxIdleTime" value="${tytrecommend_jdbc_maxIdleTime}"/>
        <property name="idleConnectionTestPeriod" value="${tytrecommend_jdbc_idleConnectionTestPeriod}"/>
        <property name="testConnectionOnCheckin" value="true"/>
        <property name="testConnectionOnCheckout" value="false"/>
        <property name="preferredTestQuery" value="SELECT 1 FROM DUAL"/>
    </bean>

    <!-- 配置SessionFactory -->
    <bean id="sessionFactoryTytRecommend"
          class="org.springframework.orm.hibernate3.annotation.AnnotationSessionFactoryBean">
        <property name="dataSource" ref="dataSourceTytRecommend"/>
        <property name="packagesToScan">
            <list><!-- 这里直接映射的pojo类所在的包,简单方便不用没次加一个pojo类都需要到这里来添加 -->
                <value>com.tytrecommend.model</value>
            </list>
        </property>
        <property name="hibernateProperties">
            <props>
                <prop key="hibernate.dialect">org.hibernate.dialect.MySQLDialect</prop>
                <prop key="hibernate.show_sql">true</prop>
                <prop key="hibernate.format_sql">true</prop>
                <prop key="hibernate.connection.autocommit">true</prop>
            </props>
        </property>
    </bean>

    <!-- HibernateTemplate -->
    <bean id="hibernateTemplateTytRecommend" class="org.springframework.orm.hibernate3.HibernateTemplate"
          lazy-init="true">
        <property name="sessionFactory" ref="sessionFactoryTytRecommend"/>
        <property name="cacheQueries" value="true"/>
    </bean>

    <!-- 配置一个事务管理器 -->
    <bean id="transactionManagerTytRecommend" class="org.springframework.orm.hibernate3.HibernateTransactionManager">
        <property name="sessionFactory" ref="sessionFactoryTytRecommend"/>
    </bean>

    <tx:advice id="txAdviceTytRecommend" transaction-manager="transactionManagerTytRecommend">
        <tx:attributes>
            <tx:method name="save*" propagation="REQUIRED" rollback-for="Exception"/>
            <tx:method name="add*" propagation="REQUIRED" rollback-for="Exception"/>
            <tx:method name="insert*" propagation="REQUIRED" rollback-for="Exception"/>
            <tx:method name="create*" propagation="REQUIRED" rollback-for="Exception"/>
            <tx:method name="update*" propagation="REQUIRED" rollback-for="Exception"/>
            <tx:method name="del*" propagation="REQUIRED" rollback-for="Exception"/>
            <tx:method name="remove*" propagation="REQUIRED" rollback-for="Exception"/>
            <tx:method name="clear*" propagation="REQUIRED" rollback-for="Exception"/>
            <tx:method name="disabled*" propagation="REQUIRED" rollback-for="Exception"/>
            <tx:method name="query*" propagation="REQUIRED" read-only="true"/>
            <tx:method name="get*" propagation="REQUIRED" read-only="true"/>
            <tx:method name="isExist*" propagation="REQUIRED" read-only="true"/>
            <tx:method name="query*" propagation="REQUIRED" read-only="true"/>
            <tx:method name="is*" propagation="REQUIRED" read-only="true"/>
            <tx:method name="read*" propagation="REQUIRED" read-only="true"/>
            <tx:method name="find*" propagation="REQUIRED" read-only="true"/>
            <tx:method name="count*" propagation="REQUIRED" read-only="true"/>
            <!-- 			<tx:method name="*" read-only="true" /> -->
        </tx:attributes>
    </tx:advice>

    <aop:config expose-proxy="true">
        <aop:advisor pointcut="execution(public * com.tytrecommend.*.service.*.*(..))"
                     advice-ref="txAdviceTytRecommend"/>
    </aop:config>

    <!-- tx:annotation 自动配置事务，注意这个标签必需放在tx:advice下面，否则不起作用 -->
    <tx:annotation-driven/>

    <!-- ==================== mybatis support ==================== -->

    <bean id="recommendMybatisDataSource" class="com.alibaba.druid.pool.DruidDataSource" destroy-method="close">
        <property name="url" value="${tytrecommend_jdbc_url}"/>
        <property name="username" value="${tytrecommend_jdbc_username}"/>
        <property name="password" value="${tytrecommend_jdbc_password}"/>

        <property name="initialSize" value="10"/>
        <property name="minIdle" value="10"/>
        <property name="maxActive" value="100"/>

        <property name="maxWait" value="1000"/>

        <property name="timeBetweenEvictionRunsMillis" value="60000"/>

        <property name="minEvictableIdleTimeMillis" value="300000"/>

        <property name="testWhileIdle" value="true"/>
        <property name="testOnBorrow" value="false"/>
        <property name="testOnReturn" value="false"/>
        <property name="validationQuery" value="SELECT 1"/>
        <property name="poolPreparedStatements" value="true"/>
        <property name="maxPoolPreparedStatementPerConnectionSize" value="20"/>

        <property name="filters" value="stat"/>
    </bean>

    <bean id="recommendMybatisSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="recommendMybatisDataSource" />
        <!-- 引入映射文件 -->
        <property name="mapperLocations" value="classpath:mapper/recommend/*.xml" />
    </bean>

    <!-- 只有再该包下才使用当前数据源 -->
    <bean id="recommendMapperScanner" class="tk.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.tyt.plat.mapper.recommend"/>
        <property name="sqlSessionFactoryBeanName" value="recommendMybatisSqlSessionFactory" />
    </bean>

</beans>
