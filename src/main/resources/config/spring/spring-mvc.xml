<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:p="http://www.springframework.org/schema/p"
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:mvc="http://www.springframework.org/schema/mvc"
	xsi:schemaLocation="http://www.springframework.org/schema/beans 
	http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
	http://www.springframework.org/schema/context
	http://www.springframework.org/schema/context/spring-context-4.0.xsd
	http://www.springframework.org/schema/mvc
	http://www.springframework.org/schema/mvc/spring-mvc-4.0.xsd"
	default-autowire="byName">

	
	<context:component-scan base-package="com.tyt.**.controller" />

	<context:component-scan base-package="com.tytrecommend.recommend.controller" />

	<!-- 开启注解 -->
	<mvc:annotation-driven>
		<!-- 解决返回String时乱码 -->
		<mvc:message-converters register-defaults="true">
			<bean class="org.springframework.http.converter.json.MappingJackson2HttpMessageConverter">
				<property name="objectMapper">
					<bean class="com.fasterxml.jackson.databind.ObjectMapper">
						<property name="serializationInclusion">
							<value type="com.fasterxml.jackson.annotation.JsonInclude.Include">NON_NULL</value>
						</property>
					</bean>
				</property>
			</bean>

			<bean class="org.springframework.http.converter.StringHttpMessageConverter">
				<constructor-arg value="UTF-8"/>
				<property name="supportedMediaTypes">
					<list>
						<value>text/plain;charset=UTF-8</value>
						<value>text/html;charset=UTF-8</value>
					</list>
				</property>
			</bean>
		</mvc:message-converters>

		<!-- 禁用 类似这种任意后缀名的url访问方式，如：/login.do /login.action /login.json -->
		<mvc:path-matching suffix-pattern="false" />
	</mvc:annotation-driven>
	

	<!-- 静态资源(js/image)的访问 -->

	<!-- 定义视图解析器 -->
	<bean id="viewResolver"
		class="org.springframework.web.servlet.view.InternalResourceViewResolver">
		<property name="viewClass"
			value="org.springframework.web.servlet.view.JstlView" />
		<property name="prefix" value="/"></property>
		<property name="suffix" value=".jsp"></property>
	</bean>
	<!-- 上传文件 -->
	<bean id="multipartResolver"
		class="org.springframework.web.multipart.commons.CommonsMultipartResolver">
		<property name="maxUploadSize" value="10485760" />
	</bean>
	<bean id="messageSource"
		class="org.springframework.context.support.ResourceBundleMessageSource"
		p:basename="i18n/messages" />

	<!-- Mapping exception to the handler view -->
	<bean id="exceptionResolver"
		class="org.springframework.web.servlet.handler.SimpleMappingExceptionResolver">
		<property name="exceptionMappings">
			<props>
			</props>
		</property>
	</bean>

</beans>

