<?xml version="1.0" encoding="utf-8"?>
<!-- 测试环境 -->
<something-else-entirely>
	<proxool>
		<alias>dbpool</alias>
		<driver-url>**********************************************************************************************</driver-url>
		<driver-class>com.mysql.jdbc.Driver</driver-class>
		<driver-properties>
			<property name="user" value="tyt_dev" />
			<property name="password" value="tyt_dev#20200724" />
			<property name="useUnicode" value="true" />
			<property name="autoReconnect" value="true" /> 
		</driver-properties>

		<house-keeping-sleep-time>90000</house-keeping-sleep-time>
		<prototype-count>2</prototype-count>
		<maximum-connection-count>50</maximum-connection-count>
		<minimum-connection-count>20</minimum-connection-count>
		<maximum-new-connections>10</maximum-new-connections>  
        <test-before-use>true</test-before-use>  
        <verbose>true</verbose>  
        <trace>true</trace> 
		<proxool.simultaneous-build-throttle>100</proxool.simultaneous-build-throttle>
		<house-keeping-test-sql>select CURRENT_DATE</house-keeping-test-sql>
	</proxool>
</something-else-entirely>
<!-- 生产环境 -->
<!-- <something-else-entirely>
	<proxool>
		<alias>dbpool</alias>
		<driver-url>**************************************************************</driver-url>
		<driver-class>com.mysql.jdbc.Driver</driver-class>
		<driver-properties>
			<property name="user" value="tytlog" />
			<property name="password" value="0678d7b0" />
			<property name="useUnicode" value="true" />
			<property name="autoReconnect" value="true" /> 
		</driver-properties>

		<house-keeping-sleep-time>90000</house-keeping-sleep-time>
		<prototype-count>2</prototype-count>
		<maximum-connection-count>50</maximum-connection-count>
		<minimum-connection-count>20</minimum-connection-count>
		<maximum-new-connections>10</maximum-new-connections>  
        <test-before-use>true</test-before-use>  
        <verbose>true</verbose>  
        <trace>true</trace> 
		<proxool.simultaneous-build-throttle>100</proxool.simultaneous-build-throttle>
		<house-keeping-test-sql>select CURRENT_DATE</house-keeping-test-sql>
	</proxool>
</something-else-entirely> -->