<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://java.sun.com/xml/ns/javaee" xmlns:web="http://java.sun.com/xml/ns/javaee/web-app_3_0.xsd"
         xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-app_3_0.xsd"
         id="WebApp_ID" version="3.0">
    <display-name>tyt_plat</display-name>
    <welcome-file-list>
        <welcome-file></welcome-file>
    </welcome-file-list>

    <!--省市县级联servlet -->
    <!-- <servlet> <servlet-name>ProvinceServlet</servlet-name> <servlet-class>com.tyt.util.ProvinceServlet</servlet-class>
        </servlet> servlet-mapping是指根据别名，定义访问方式 <servlet-mapping> <servlet-name>ProvinceServlet</servlet-name>
        <url-pattern>/ProvinceServlet</url-pattern> </servlet-mapping> -->


    <!-- 加载所有的配置文件 -->
    <context-param>
        <param-name>contextConfigLocation</param-name>
        <param-value>classpath*:config/spring/spring-common.xml,classpath*:config/spring/spring-tytrecommend.xml
        </param-value>
    </context-param>

    <servlet>
        <servlet-name>ServletConfigurator</servlet-name>
        <servlet-class>
            org.logicalcobwebs.proxool.configuration.ServletConfigurator
        </servlet-class>
        <init-param>
            <param-name>xmlFile</param-name>
            <param-value>WEB-INF/classes/proxool.xml</param-value>
        </init-param>
        <load-on-startup>1</load-on-startup>
    </servlet>

    <!-- 配置Spring监听 -->
    <listener>
        <listener-class>org.springframework.web.context.ContextLoaderListener</listener-class>
    </listener>

    <!-- 配置SpringMVC -->
    <servlet>
        <servlet-name>springMVC</servlet-name>
        <servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
        <init-param>
            <param-name>contextConfigLocation</param-name>
            <param-value>classpath*:config/spring/spring-mvc.xml</param-value>
        </init-param>
        <load-on-startup>1</load-on-startup>
    </servlet>
    <servlet-mapping>
        <servlet-name>springMVC</servlet-name>
        <url-pattern>/</url-pattern>
    </servlet-mapping>

    <!-- 日志唯一标识filter -->
    <filter>
        <filter-name>webLogFilter</filter-name>
        <filter-class>org.springframework.web.filter.DelegatingFilterProxy</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>webLogFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <!--重复读流过滤器-->
    <filter>
        <filter-name>repeatedlyReadFilter</filter-name>
        <filter-class>org.springframework.web.filter.DelegatingFilterProxy</filter-class>
        <init-param>
            <param-name>targetFilterLifecycle</param-name>
            <param-value>true</param-value>
        </init-param>
    </filter>
    <filter-mapping>
        <filter-name>repeatedlyReadFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <!-- jfreechart -->
    <servlet>
        <servlet-name>DisplayChart</servlet-name>
        <servlet-class>org.jfree.chart.servlet.DisplayChart</servlet-class>
    </servlet>

    <servlet-mapping>
        <servlet-name>DisplayChart</servlet-name>
        <url-pattern>/servlet/DisplayChart</url-pattern>
    </servlet-mapping>

    <!-- 限制接口并发访问次数 -->
    <filter>
        <filter-name>requestLimitFilter</filter-name>
        <filter-class>org.springframework.web.filter.DelegatingFilterProxy</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>requestLimitFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <filter>
        <filter-name>filter1</filter-name>
        <filter-class>org.springframework.web.filter.DelegatingFilterProxy</filter-class>
        <init-param>
            <param-name>targetFilterLifecycle</param-name>
            <param-value>true</param-value>
        </init-param>
    </filter>
    <filter-mapping>
        <filter-name>filter1</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>
    <!-- 配置字符集 -->
    <filter>
        <filter-name>encodingFilter</filter-name>
        <filter-class>org.springframework.web.filter.CharacterEncodingFilter</filter-class>
        <init-param>
            <param-name>encoding</param-name>
            <param-value>UTF-8</param-value>
        </init-param>
        <init-param>
            <param-name>ignore</param-name>
            <param-value>true</param-value>
        </init-param>
    </filter>
    <filter-mapping>
        <filter-name>encodingFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <!-- 合法客户端验证+ 签名验证 -->
    <filter>
        <filter-name>legalRequestFilter</filter-name>
        <filter-class>org.springframework.web.filter.DelegatingFilterProxy</filter-class>
        <init-param>
            <param-name>targetFilterLifecycle</param-name>
            <param-value>true</param-value>
        </init-param>
    </filter>
    <filter-mapping>
        <filter-name>legalRequestFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

<!--    &lt;!&ndash; 保护URL拦截过滤 &ndash;&gt;-->
    <filter>
        <filter-name>securityInterceptFilter</filter-name>
        <filter-class>org.springframework.web.filter.DelegatingFilterProxy</filter-class>
        <init-param>
            <param-name>targetFilterLifecycle</param-name>
            <param-value>true</param-value>
        </init-param>
    </filter>
    <filter-mapping>
        <filter-name>securityInterceptFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <!-- 配置Session -->
    <filter>
        <filter-name>openSession</filter-name>
        <filter-class>org.springframework.orm.hibernate3.support.OpenSessionInViewFilter</filter-class>
        <init-param>
            <param-name>sessionFactoryBeanName</param-name>
            <param-value>sessionFactory</param-value>
        </init-param>
        <init-param>
            <param-name>singleSession</param-name>
            <param-value>true</param-value>
        </init-param>
        <init-param>
            <param-name>flushMode</param-name>
            <param-value>AUTO</param-value>
        </init-param>

    </filter>
    <filter-mapping>
        <filter-name>openSession</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>
    <!-- 控制频繁访问服务器的过滤器 -->
    <filter>
        <filter-name>frequentlyVisitFilter</filter-name>
        <filter-class>com.tyt.util.filter.FrequentlyVisitFilter</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>frequentlyVisitFilter</filter-name>
        <url-pattern>/plat/transport/getPhone*</url-pattern>
    </filter-mapping>

    <filter>
        <filter-name>xssFilter</filter-name>
        <filter-class>org.springframework.web.filter.DelegatingFilterProxy</filter-class>
        <init-param>
            <param-name>targetFilterLifecycle</param-name>
            <param-value>true</param-value>
        </init-param>
    </filter>
    <filter-mapping>
        <filter-name>xssFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <!-- 连接池监控 -->
    <servlet>
        <servlet-name>proxool</servlet-name>
        <servlet-class>org.logicalcobwebs.proxool.admin.servlet.AdminServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>proxool</servlet-name>
        <url-pattern>/proxool</url-pattern>
    </servlet-mapping>
    <distributable/>
    <!-- session timeout -->
    <session-config>
        <session-timeout>30</session-timeout>
    </session-config>

</web-app>
