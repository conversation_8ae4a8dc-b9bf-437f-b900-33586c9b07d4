package test;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tyt.acvitity.dao.ProdCouponDao;
import com.tyt.infofee.service.CardPrefixBankInfoService;
import com.tyt.util.Constant;
import org.apache.commons.lang.StringUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;
import org.wltea.analyzer.core.IKSegmenter;
import org.wltea.analyzer.core.Lexeme;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.StringReader;
import java.util.ArrayList;
import java.util.List;

@ContextConfiguration(locations={"classpath:/config/spring/spring-mvc.xml","classpath:/config/spring/spring-common.xml","classpath:/config/spring/spring-tytrecommend.xml"})
public class TTTTTest  extends AbstractJUnit4SpringContextTests {

	@Test
	public void testKeyword(){
		List<String> keywords = new ArrayList<String>();
		IKSegmenter ik = new IKSegmenter(new StringReader("这是一家水果店"), false);
		try {
			Lexeme word = null;
			while((word=ik.next())!=null) {
					keywords.add(word.getLexemeText());
			}		} catch (IOException ex) {
			throw new RuntimeException(ex);
		}
		for (String str : keywords) {
			System.out.print(str+"  ");
		}
	}

	@Resource(name = "cardPrefixBankInfoService")
	private CardPrefixBankInfoService cardPrefixBankInfoService;

	@Autowired
	private ProdCouponDao prodCouponDao;

	@Test
	public void testBankName(){
		System.out.println("开始查询");
		String cardId = "9558820200001323775";
		String cardIdPrefix = cardId.substring(0,6);
		//根据前缀获取缓存的银行名称
		String bankKey = Constant.CARDID_PREFIX_BANKNAME+cardIdPrefix;
		String bankName = cardPrefixBankInfoService.getCacheStringByInit(bankKey);
		if(StringUtils.isEmpty(bankName)){
			//如果不存在则去调用阿里接口，并保存查询结果
			bankName = cardPrefixBankInfoService.addRemoteAliBankName(cardId);
		}
		System.out.println("查询结果："+bankName);
	}

	@Test
	public void getCoupons(){

		System.out.println("format1");
	}


	public static void main(String[] args) throws Exception{

		boolean a = true;

		Boolean b = true;


		ObjectMapper mapper = new ObjectMapper();

		String json = mapper.writeValueAsString(a);

		String json1 = mapper.writeValueAsString(b);

		System.out.println(json);

	}

}
