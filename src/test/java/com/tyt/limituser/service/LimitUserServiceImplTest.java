package com.tyt.limituser.service;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.junit.Assert.*;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations={
        "classpath:/config/spring/spring-common.xml",
        "classpath:/config/spring/spring-tytrecommend.xml"})
public class LimitUserServiceImplTest {


    @Autowired
    private ILimitUserService limitUserService;

    @Test
    public void isLimitUser() {
        String userID = "147622";
        boolean isLimit = limitUserService.isLimitUser(userID);
        assertTrue(isLimit);

        userID = "1";
        isLimit = limitUserService.isLimitUser(userID);
        assertFalse(isLimit);
    }
}