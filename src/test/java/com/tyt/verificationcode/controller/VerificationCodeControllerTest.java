package com.tyt.verificationcode.controller;

import com.alibaba.fastjson.JSON;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.User;
import com.tyt.plat.test.base.TytTestBase;
import com.tyt.user.service.UserService;
import com.tyt.verificationcode.bean.SendMsgResultBean;
import junit.framework.TestCase;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2024/1/22 15:29
 */
public class VerificationCodeControllerTest extends TytTestBase {

    @Autowired
    private VerificationCodeController verificationCodeController;

    @Autowired
    private UserService userService;

    @Test
    public void testUserAuthValid() throws Exception{
        Long userId = 1002000573L;

        User dbUser = userService.getByUserId(userId);

        SendMsgResultBean sendMsgResultBean = verificationCodeController.sendCode(dbUser.getCellPhone(), dbUser.getId().intValue(), 11, null);

        System.out.println(JSON.toJSONString(sendMsgResultBean));

        String validCode = "123123";
        ResultMsgBean resultMsgBean = verificationCodeController.userAuthValid(userId, validCode);

        assertTrue(true);
    }



}