package com.tyt.orderlimit.service;

import com.gexin.fastjson.JSON;
import com.tyt.base.bean.BaseParameter;
import com.tyt.orderlimit.bean.AcceptOrderLimitInfo;
import com.tyt.transport.querybean.CallPhoneSearchResultBean;
import com.tyt.transport.service.CallPhoneListService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations={
        "classpath:/config/spring/spring-common.xml",
        "classpath:/config/spring/spring-tytrecommend.xml"})
public class AcceptOrderLimitRecordServiceImplTest {


    @Autowired
    private AcceptOrderLimitRecordService acceptOrderLimitRecordService;

    @Autowired
    private  CallPhoneListService callPhoneListService;

    @Test
    public void getAcceptOrderLimitInfo() {
        Long userId = 1002000068L;
        Long goodsId = 33858809L;

        AcceptOrderLimitInfo acceptOrderLimitInfo = null;
        try {
            acceptOrderLimitInfo = acceptOrderLimitRecordService.getAcceptOrderLimitInfo(userId, goodsId);
            System.out.println(JSON.toJSONString(acceptOrderLimitInfo));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }


    @Test
    public void addGetCallPhoneList() {
        String goodsId = "33869784";
        String moduleType  = "2";
        String path = "findGoodsPage2openGoodsItem_goodsDetailPage2getPhone";
        BaseParameter base = new BaseParameter();
        base.setClientSign("31");
        base.setOsVersion("18");
        base.setClientVersion("6401");
        base.setClientId("8ECBB762-31D0-4779-83C1-D505366B1052");
        base.setSign("46514296b5cd576c030f928f3e7c3f7b");
        base.setUserId(1002000942L);
        base.setTicket("695b0f3518871413b0f450d2b9f9bfe7");

        CallPhoneSearchResultBean resultBean = null;
        try {
            resultBean = callPhoneListService.addGetCallPhoneList(goodsId,moduleType,path,base);
            System.out.println(JSON.toJSONString(resultBean));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }
}