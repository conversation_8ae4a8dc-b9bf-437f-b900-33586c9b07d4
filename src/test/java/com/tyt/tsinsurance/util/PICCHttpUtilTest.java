package com.tyt.tsinsurance.util;

import com.tyt.tsinsurance.bean.picc.PICCBusinessException;
import com.tyt.tsinsurance.bean.picc.SubmitInsuranceBean;
import com.tyt.tsinsurance.service.IPICCInsuranceService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;


@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations={
        "classpath:/config/spring/spring-common.xml",
        "classpath:/config/spring/spring-tytrecommend.xml"})
public class PICCHttpUtilTest {

    private static final String DATE_FORMAT_SECOND = "yyyy-MM-dd HH:mm:ss"; //2018-11-27 15:38:23
    private static final String DATE_FORMAT_HOUR = "yyyy-MM-dd:HH"; //2018-11-27:15


    @Autowired
    private IPICCInsuranceService insuranceService;


    @Test
    public void testComposeEpolicyUrl(){
        //http://**************:8940/getPolicyDownUrl?pNo=PYDG201835020097E71456&sign=6d51548be25a134f84b41d24c83674ea
        String ePolicyUrl = PICCHttpUtil.composeEPolictyUrl("PYDG201835020097E71456");
        System.out.println(ePolicyUrl);
        Assert.assertEquals("http://**************:8940/getPolicyDownUrl?pNo=PYDG201835020097E71456&sign=6d51548be25a134f84b41d24c83674ea", ePolicyUrl);
    }

    @Test
    public void testSubmitOrder() throws PICCBusinessException {
        String orderNo = String.valueOf(System.currentTimeMillis() / 1000);


        SimpleDateFormat dateFormatSecond = new SimpleDateFormat(DATE_FORMAT_SECOND);
        SimpleDateFormat dateFormatHour = new SimpleDateFormat(DATE_FORMAT_HOUR);
        Date now = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(now);
        calendar.add(Calendar.DAY_OF_YEAR, 1);
        Date nextDay = calendar.getTime();
        SubmitInsuranceBean submitInsuranceBean = new SubmitInsuranceBean();
        submitInsuranceBean.setInsuranceNo(orderNo);
        submitInsuranceBean.setInsurantCardType("01");
        submitInsuranceBean.setApplicantCardNo("110101199102062324");
        submitInsuranceBean.setApplicantCardType("01");
        submitInsuranceBean.setApplicantName("满十八");
        submitInsuranceBean.setInsurantCardNo("110101199102062324");
        submitInsuranceBean.setInsurantName("满十八");
        submitInsuranceBean.setAmount("10000");
        submitInsuranceBean.setPremium("30");
        submitInsuranceBean.setFromLoc("北京");
        submitInsuranceBean.setToLoc("天津");
        submitInsuranceBean.setGoodsName("挖机");
        submitInsuranceBean.setPackQty("1台");
        submitInsuranceBean.setStartTime(new SimpleDateFormat(DATE_FORMAT_SECOND).format(nextDay));
        submitInsuranceBean.setDepartureDate(new SimpleDateFormat(DATE_FORMAT_HOUR).format(nextDay));
        String payUrl = insuranceService.getPICCPayUrl(submitInsuranceBean);
        Assert.assertNotNull(payUrl);
        System.out.println(payUrl);

    }

    @Test
    public void testGetEpolicyUrl() {
        String policyUrl = "611009906000501180001007";
        try {
            String ePolicyUrl = insuranceService.getEPolicyUrl(policyUrl);
            Assert.assertNotNull(ePolicyUrl);
        } catch (PICCBusinessException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void doPICCPost() {
        String param = "{\"system\":\"S10000038\",\"interface\":\"100052\",\"mode\":\"\"}";
        String data = "{\n" +
                "  \"head\": {\n" +
                "    \"transactionNo\": \"1543230641\",\n" +
                "    \"operator\": \"S10000038\",\n" +
                "    \"timeStamp\": \"2018-11-26 11:10:41\",\n" +
                "    \"errorCode\": \"0000\",\n" +
                "    \"errorMsg\": \"\"\n" +
                "  },\n" +
                "  \"body\": {\n" +
                "    \"applicant\": {\n" +
                "      \"cardNo\": \"110101199102062324\",\n" +
                "      \"cardType\": \"01\",\n" +
                "      \"name\": \"满十八\",\n" +
                "      \"birthday\": \"\",\n" +
                "      \"sex\": \"\",\n" +
                "      \"mobile\": \"\"\n" +
                "    },\n" +
                "    \"insurants\": [\n" +
                "      {\n" +
                "        \"cardNo\": \"110101199102062324\",\n" +
                "        \"cardType\": \"01\",\n" +
                "        \"insurerKey\": \"1543230641#tyt\",\n" +
                "        \"name\": \"满十八\",\n" +
                "        \"relation\": \"0\",\n" +
                "        \"birthday\": \"\",\n" +
                "        \"sex\": \"\",\n" +
                "        \"amount\": \"10000\",\n" +
                "        \"premium\": \"100\",\n" +
                "        \"mobile\": \"\"\n" +
                "      }\n" +
                "    ],\n" +
                "    \"orderInfo\": {\n" +
                "      \"startTime\": \"2018-11-27 19:10:41\",\n" +
                "      \"orderNo\": \"1543230641\",\n" +
                "      \"productNo\": \"ABX10000084\",\n" +
                "      \"channelId\": \"CH10000342\",\n" +
                "      \"sumAmount\": \"10000\",\n" +
                "      \"sumPremium\": \"100\"\n" +
                "    },\n" +
                "    \"targets\": {\n" +
                "      \"packQty\": \"1台\",\n" +
                "      \"goodsName\": \"挖机\",\n" +
                "      \"goodsTypeNo\": \"630602\",\n" +
                "      \"transportType\": \"公路\",\n" +
                "      \"transport\": \"汽车\",\n" +
                "      \"fromLoc\": \"北京\",\n" +
                "      \"toLoc\": \"天津\",\n" +
                "      \"departureDate\": \"2018-11-27:19\",\n" +
                "      \"ratio\": \"30\"\n" +
                "    },\n" +
                "    \"extendInfo\": {\n" +
                "      \"callbackURL\": \"http://www.teyuntong.com\"\n" +
                "    }\n" +
                "  }\n" +
                "}";
        try {
            String response = PICCHttpUtil.doPICCPost(param, data);
            System.out.println(response);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}