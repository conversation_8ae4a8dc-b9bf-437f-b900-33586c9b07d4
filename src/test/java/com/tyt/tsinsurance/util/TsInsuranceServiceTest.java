package com.tyt.tsinsurance.util;

import com.tyt.tsinsurance.bean.CalcCostBean;
import com.tyt.tsinsurance.bean.picc.PICCBusinessException;
import com.tyt.tsinsurance.bean.picc.SubmitInsuranceBean;
import com.tyt.tsinsurance.service.IPICCInsuranceService;
import com.tyt.tsinsurance.service.TsInsuranceService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;


@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations={
        "classpath:/config/spring/spring-common.xml",
        "classpath:/config/spring/spring-tytrecommend.xml"})
public class TsInsuranceServiceTest {


    @Autowired
    private TsInsuranceService insuranceService;

    @Test
    public void testCalcCost() throws PICCBusinessException {
        //保额 单位:万元
        String amtCurrency = "10";
        String startProvinc = "西藏";
        String startCity = "拉萨市";
        String startArea = "";
        String destProvinc = "北京";
        String destCity = "北京市";
        String destArea = "";
        //保费对象
        CalcCostBean calcCostBean = insuranceService.calcCost(amtCurrency, startProvinc, startCity, startArea, destProvinc, destCity, destArea);
        Assert.assertNotNull(calcCostBean);
        System.out.println(calcCostBean.getPremiumCurrency());

    }

}