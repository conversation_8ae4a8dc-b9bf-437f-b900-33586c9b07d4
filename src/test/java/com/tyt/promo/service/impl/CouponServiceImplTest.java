package com.tyt.promo.service.impl;

import com.alibaba.fastjson.JSON;
import com.tyt.messagecenter.core.utils.CommonUtil;
import com.tyt.model.ResultMsgBean;
import com.tyt.plat.vo.coupon.GoodsUserCoupon;
import com.tyt.promo.model.CarRedPacketCouponDetail;
import com.tyt.promo.model.RedPacketCouponBean;
import com.tyt.promo.model.UserCoupon;
import com.tyt.promo.service.ICouponService;
import com.tyt.promo.service.PromoGiftCouponService;
import com.tyt.user.service.TytConfigService;
import com.tytrecommend.model.CreditDeductLog;
import com.tytrecommend.recommend.controller.CreditDeductController;
import com.tytrecommend.recommend.service.CreditDeductLogService;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations={
        "classpath:/config/spring/spring-common.xml",
        "classpath:/config/spring/spring-tytrecommend.xml"})
public class CouponServiceImplTest {

    @Autowired
    private ICouponService couponService;
    @Resource(name="promoGiftCouponService")
    private PromoGiftCouponService giftCouponService;
    @Resource(name = "tytConfigService")
    private TytConfigService tytConfigService;

    @Resource(name = "creditDeductLogService")
    private CreditDeductLogService creditDeductLogService;

    @Test
    @Ignore
    public void queryCouponListByUserId() {
//        List<UserCoupon> couponList = couponService.queryCouponListByUserId("", 1, 1, 100);

//        Assert.assertNotNull(couponList);
//        ResultMsgBean result=new ResultMsgBean();
//        List<UserCoupon> coupons = couponService.queryCouponListByUserId(147785l, 1, 0, 1, 2,null);
//        result.setData(coupons);
//        result.setCode(200);
//        System.out.println(result);

    }

    @Test
    @Ignore
    public void testGetValidRemainQtyByUserId() {
//        int remainQty = couponService.getValidRemainQtyByUserId(14775);
//        System.err.println("_______________"+remainQty);
        ResultMsgBean result=new ResultMsgBean();
        Map<String, Object> map = giftCouponService.getGiftDetailForGiftId(3,45808,148883L);
        result.setData(map);
        System.out.println(result);

    }
    @Test
    public void tes() {
        List<CreditDeductLog> creditDeduct = creditDeductLogService.getListByUserId(123L,1,null);
        System.out.println(JSON.toJSONString(creditDeduct));
        System.out.println("--------------------over");
    }

    @Test
    public  void queryCarRedPacketCouponListByUserId() throws Exception {
        CarRedPacketCouponDetail carRedPacketCouponDetail = couponService.queryCarRedPacketCouponListByUserId(147852L);
        System.out.println(JSON.toJSONString(carRedPacketCouponDetail));
    }

    @Test
    public  void queryRedPacketCouponListByUserId() throws Exception {
        List<RedPacketCouponBean> couponBeanList = couponService.queryRedPacketCouponListByUserId(1002000942L, 2);
        System.out.println(JSON.toJSONString(couponBeanList));
    }

    @Test
    public  void getMaxAmountCouponByUserId() throws Exception {
        UserCoupon userCoupon = couponService.getMaxAmountCoupon(1002000942L, 2);
        System.out.println(JSON.toJSONString(userCoupon));
    }

    @Test
    public  void getExpireCouponByUserId() throws Exception {
        List<UserCoupon> userCoupon = couponService.getExpireCoupons(1002000942L, 2);
        System.out.println(JSON.toJSONString(userCoupon));
    }

    @Test
    public  void updateCouponStatus() throws Exception {
         couponService.updateCouponStatus(1002000942L,102294);
    }

    @Test
    public  void getCouponList() throws Exception {
        List<Integer> scopeDetailList = CommonUtil.arraySplitInteger("1,26,27");
        List<GoodsUserCoupon> userCoupon = couponService.getAllValidCoupon(1002000807L, 27,scopeDetailList);
        System.out.println(JSON.toJSONString(userCoupon));
    }

}