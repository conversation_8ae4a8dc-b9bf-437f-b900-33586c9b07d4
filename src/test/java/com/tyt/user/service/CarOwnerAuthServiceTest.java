package com.tyt.user.service;

import com.alibaba.fastjson.JSON;
import com.tyt.model.ResultMsgBean;
import com.tyt.plat.entity.base.TytCarOwnerAuth;
import com.tyt.plat.mapper.base.TytCarOwnerAuthMapper;
import com.tyt.plat.test.base.TytTestBase;
import com.tyt.user.bean.CarOwnerAuthVO;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/02/19 10:36
 */
public class CarOwnerAuthServiceTest extends TytTestBase {
    @Autowired
    private CarOwnerAuthService carOwnerAuthService;
    @Autowired
    private TytCarOwnerAuthMapper tytCarOwnerAuthMapper;

    @Test
    public void carOwnerAuthServiceTest() throws Exception {
        Integer quickCommit = carOwnerAuthService.isQuickCommit(1002000313L,4770L);
        System.out.println(quickCommit);
    }

    @Test
    public void quickCommitTest() throws Exception {
        Long userId=1002000313L;
        Long carId=4770L;
        ResultMsgBean resultMsgBean = carOwnerAuthService.quickCommit(userId, carId);
        System.out.println(JSON.toJSONString(resultMsgBean));
    }

    @Test
    public void selectConvertMapTest(){
        Long userId=1002000313L;
        Map<Long, TytCarOwnerAuth> longTytCarOwnerAuthMap = tytCarOwnerAuthMapper.selectConvertMap(userId);
        System.out.println(longTytCarOwnerAuthMap);
    }

    @Test
    public void getCarOwnerInfoTest(){
        CarOwnerAuthVO carOwnerInfo = carOwnerAuthService.getCarOwnerInfo(1002000755L);
        System.out.println(carOwnerInfo);
    }
}
