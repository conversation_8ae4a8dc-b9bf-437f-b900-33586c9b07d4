package com.tyt.user.service.impl;

import com.tyt.plat.test.base.TytTestBase;
import com.tyt.user.service.TytUserIdentityAuthService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2024/1/11 13:23
 */
public class TytUserIdentityAuthServiceImplTest extends TytTestBase {

    @Autowired
    private TytUserIdentityAuthService tytUserIdentityAuthService;

    @Test
    public void testGetRealVerifyCount() {

        Long userId = 10003L;

        int realVerifyCount = tytUserIdentityAuthService.getRealVerifyCount(userId);

        System.out.println(realVerifyCount);

        assertTrue(true);
    }
}