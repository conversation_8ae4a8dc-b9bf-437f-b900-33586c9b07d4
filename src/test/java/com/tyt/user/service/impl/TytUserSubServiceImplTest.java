package com.tyt.user.service.impl;

import com.tyt.messagecenter.core.utils.CommonUtil;
import org.junit.Test;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2022/4/13 10:25
 */
public class TytUserSubServiceImplTest {

    @Test
    public void checkDeviceId(){

        String oneDevice= "aa中文aaa测试12";
        String reg = "[\u4e00-\u9fa5]+";

        boolean matches = oneDevice.matches(reg);

        Pattern pattern = Pattern.compile(reg);
        Matcher matcher = pattern.matcher(oneDevice);

        boolean result = matcher.find();

        System.out.println("finished ... ");
    }

}