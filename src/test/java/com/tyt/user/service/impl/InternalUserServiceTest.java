package com.tyt.user.service.impl;

import com.tyt.plat.test.base.TytTestBase;
import com.tyt.user.bean.UserBaseInfo;
import com.tyt.user.service.InternalUserService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class InternalUserServiceTest extends TytTestBase {
    @Autowired
    private InternalUserService internalUserService;

    @Test
    public void userTest(){
        // 违规 tyt_user_violation_info
        //     tyt_user_violation_log
        UserBaseInfo byUserId = internalUserService.getByUserId(4645L);
        System.out.println(byUserId);
    }
}
