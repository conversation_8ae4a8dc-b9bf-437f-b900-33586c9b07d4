package com.tyt.user.service;

import com.tyt.plat.test.base.TytTestBase;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2024/1/19 18:13
 */
public class TytUserIdentityAuthServiceTest extends TytTestBase {

    @Autowired
    private TytUserIdentityAuthService tytUserIdentityAuthService;

    @Test
    public void testSaveEnterprise() {

        Long userId = 1002000279L;
        String enterpriseAuthLicenseUrl = "";
        String enterpriseAuthPath = "";
        Integer clientSign = 21;

        tytUserIdentityAuthService.saveEnterprise(userId, enterpriseAuthLicenseUrl, enterpriseAuthPath, clientSign);

        System.out.println("finished ... ");
        assert true;

    }

    public void testGetByUserId() {
    }

    public void testSaveIdentityAuthMove() {
    }

    public void testSave() {
    }

    public void testSaveFaceVerifyV5() {
    }
}