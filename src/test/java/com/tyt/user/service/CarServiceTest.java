package com.tyt.user.service;

import com.alibaba.fastjson.JSONObject;
import com.tyt.messagecenter.core.utils.CommonUtil;
import com.gexin.fastjson.JSON;
import com.tyt.messagecenter.core.utils.CommonUtil;
import com.tyt.model.Car;
import com.tyt.model.ResultMsgBean;
import com.tyt.plat.entity.base.TytInvoiceDriver;
import com.tyt.plat.service.api.CommonApiService;
import com.tyt.plat.test.base.TytTestBase;
import com.tyt.plat.vo.ocr.VehicleLicenseFrontVo;
import com.tyt.service.common.enums.ResponseEnum;
import com.tyt.user.bean.MyFullCarListBean;
import com.tyt.user.bean.UserCarInfoVO;
import com.tyt.user.querybean.UserCarBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/01/17 16:14
 */
@Slf4j
public class CarServiceTest extends TytTestBase {
    @Autowired
    private CarService carService;
    @Autowired
    private CommonApiService commonApiService;


    @Test
    public void ocrDrivingLicenseInfoTest(){
        //String url = "http://devimage.teyuntong.net/dispatch/APP/2024-01-17/010020002743fa8c33ac522ba6e0e1660a8d3ecb75e.jpg";
        //String url = "https://api.teyuntong.net/manage_new/root_pic/data/pictures/car/2401091322337233.jpg";
        //String url = "https://5b0988e595225.cdn.sohucs.com/images/20191215/4d58fd2f1bdb4b9c886b0ed07a0c43dd.jpeg";
//        String url = "https://dev.teyuntong.net/uploadimg/loopPicture/1706262641577_086351.jpg";
//        String url = "https://5b0988e595225.cdn.sohucs.com/images/20190822/bf1c73f482964011bd4d667bcb7e8620.jpeg";
        String url = "https://5b0988e595225.cdn.sohucs.com/images/20190822/bf1c73f482964011bd4d667bcb7e8620.jpeg";
        ResultMsgBean resultMsgBean = carService.ocrDrivingLicenseInfo(url);
        log.info(JSONObject.toJSONString(resultMsgBean));
    }


    @Test
    public void getMergeMyCarListTest(){
        MyFullCarListBean mergeMyCarList = carService.getMergeMyCarList(1002000313L, "", null);
        System.out.println(mergeMyCarList);
    }

    @Test
    public void deleteByCarOwnerAuthTest(){
        Long carId= 4770L;
        Car car = carService.getById(carId);
        String oldHeadCity = car.getHeadCity();
        String oldHeadNo = car.getHeadNo();
        String newHeadCity = "冀";
        String newHeadNo = "A22ERT";
        //6420 如果车牌号有变化则同步删除车主认证信息
        if (StringUtils.isNotBlank(oldHeadCity) && StringUtils.isNotBlank(oldHeadNo) && StringUtils.isNotBlank(newHeadCity) && StringUtils.isNotBlank(newHeadNo)){
            if (!oldHeadCity.equals(newHeadCity) || !oldHeadNo.equals(newHeadNo)){
                carService.deleteByCarOwnerAuth(car.getId());
            }
        }
    }

    @Test
    public void selectIdsTest(){
        Long carId = null;
        List<Long> integers = carService.selectIds(1002000313L, "1");
        carService.deleteByCarOwnerAuth(carId);
        System.out.println(integers);
    }

    @Test
    public void getUserCarInfoVOTest(){
        Long carId=241422525L;
        UserCarInfoVO userCarInfoVO = carService.getUserCarInfoVO(carId);
        System.out.println(userCarInfoVO);
    }

    @Test
    public void vehicleLicenseMainOcrTest(){
        /*VehicleLicenseFrontVo vehicleLicenseFrontVo = commonApiService.vehicleLicenseMainOcr("https://5b0988e595225.cdn.sohucs.com/images/20190822/bf1c73f482964011bd4d667bcb7e8620.jpeg");
        System.out.println(vehicleLicenseFrontVo);*/
//        Map<String, List<TytInvoiceDriver>> map = carService.getCarUserList(4853L, 1002000771L, 1);
//        System.out.println(map);
    }

    /**
     * OCR识别行驶证正副页 包含提示信息
     */
    @Test
    public void getCarOcrDrivingLicenseInfoTest() {
        String ocrUrl = "https://api.teyuntong.net/manage_new/root_pic/data/pictures/car/2401091322337233.jpg";
        ResultMsgBean carOcrDrivingLicenseInfo = carService.getCarOcrDrivingLicenseInfo(ocrUrl);
        System.out.println(JSON.toJSONString(carOcrDrivingLicenseInfo));
    }

    /**
     * 根据用户id刷新车辆是否符合开票
     */
    @Test
    public void refreshIsInvoiceTest(){
        Long userId= 1002000942L;
        carService.refreshIsInvoice(userId);
    }
}
