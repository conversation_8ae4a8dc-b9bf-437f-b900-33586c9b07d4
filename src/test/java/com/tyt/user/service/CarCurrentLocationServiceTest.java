package com.tyt.user.service;

import com.alibaba.fastjson.JSON;
import com.tyt.plat.test.base.TytTestBase;
import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2024/3/18 15:44
 */
@Slf4j
public class CarCurrentLocationServiceTest extends TytTestBase {

    @Autowired
    private CarCurrentLocationService carCurrentLocationService;

    @Test
    public void testGetCurrentLocation() {
        String carNo = "豫RJF662";

        try {
            Object currentLocation = carCurrentLocationService.getCurrentLocation(carNo);

            log.info("text : {}", JSON.toJSONString(currentLocation));
        } catch (Exception e) {
            log.error("", e);
        }

//        for(int i=0; i<10; i++){
//
//            try {
//                Object currentLocation = carCurrentLocationService.getCurrentLocation(carNo);
//
//                log.info("text : {}", JSON.toJSONString(currentLocation));
//            } catch (Exception e) {
//                log.error("", e);
//            }
//
//
//        }

        assertTrue(true);

    }
}