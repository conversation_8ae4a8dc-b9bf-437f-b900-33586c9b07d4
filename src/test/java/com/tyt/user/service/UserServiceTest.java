package com.tyt.user.service;

import com.tyt.model.ResultMsgBean;
import com.tyt.model.User;
import com.tyt.plat.entity.base.CsCustomQcCode;
import com.tyt.plat.mapper.base.CsCustomQcCodeMapper;
import com.tyt.plat.test.base.TytTestBase;
import junit.framework.TestCase;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2023/7/18 11:47
 */
public class UserServiceTest extends TytTestBase {

    @Autowired
    private UserService userService;

    @Autowired
    private CsCustomQcCodeMapper csCustomQcCodeMapper;

    @Test
    public void testUpdateInitUserName() throws Exception {

        Long userId = 1000001077L;

        User dbUser = userService.getByUserId(userId);

        userService.updateInitUserName(dbUser);

        System.out.println("finished ... ");


    }

    @Test
    public void testCheckUserPublishTransportPermission() throws Exception{

        Long userId = 1002000573L;

        ResultMsgBean priceResult = userService.checkUserPublishTransportPermission(userId, "1500");

        ResultMsgBean nullResult = userService.checkUserPublishTransportPermission(userId, null);

        assertTrue(true);

    }

    @Test
    public void testQcCode(){
        CsCustomQcCode csCustomQcCode = csCustomQcCodeMapper.selectByPrimaryKey(26L);
        System.out.println("jfoiehgoi");
    }


}