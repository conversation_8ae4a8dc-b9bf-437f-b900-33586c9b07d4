package com.tyt.user.service;

import com.tyt.car.service.CarDetailHeadService;
import com.tyt.car.service.CarDetailTailService;
import com.tyt.model.CarDetailHead;
import com.tyt.model.CarDetailTail;
import com.tyt.plat.test.base.TytTestBase;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @since 2024/03/06 09:59
 */
public class CarDetailHeadServiceTest extends TytTestBase {
    @Autowired
    private CarDetailHeadService carDetailHeadService;
    @Autowired
    private CarDetailTailService carDetailTailService;

    @Test
    public void getByUserIdCarIdTest() {
        Long carId = 4856L;
        CarDetailHead byUserIdCarId = carDetailHeadService.getByCarId(4856L);
        System.out.println(byUserIdCarId);
    }

    @Test
    public void getCarDetailTailByCarId() {
        Long carId = 4856L;
        CarDetailTail byCarId = carDetailTailService.getByCarId(4856L);
        System.out.println(byCarId);
    }
}
