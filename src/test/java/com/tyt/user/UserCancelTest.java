package com.tyt.user;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.tyt.infofee.controller.UserCancelController;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.UserTel;
import com.tyt.plat.entity.base.TytUserRecord;
import com.tyt.plat.mapper.base.TytUserRecordMapper;
import com.tyt.plat.service.base.TytUserRecordService;
import com.tyt.plat.test.base.TytTestBase;
import com.tyt.plat.vo.other.IndividuationSettings;
import com.tyt.user.querybean.UserTelModel;
import com.tyt.user.service.UserCellPhoneChangeService;
import com.tyt.user.service.UserTelService;
import com.tyt.util.ReturnCodeConstant;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


public class UserCancelTest extends TytTestBase {

    @Autowired
    UserCancelController userCancelController;
    @Autowired
    private TytUserRecordMapper tytUserRecordMapper;
    @Autowired
    private TytUserRecordService tytUserRecordService;
    @Autowired
    private UserCellPhoneChangeService userCellPhoneChangeService;

    @Test
    public void userCancelNotice(){
        ResultMsgBean rm = userCancelController.userCancelNotice(1000000609L);

        System.out.println(rm);

    }

    @Test
    public void userRecordTest(){
        List<String> codes = new ArrayList<>();
        codes.add("car_user_info_auth");
        codes.add("goods_user_info_auth");

//        List<IndividuationSettings> individuationSettings = tytUserRecordMapper.selectIndividuationSettings(1000001130L, codes);
//        System.out.println(individuationSettings);
        List<IndividuationSettings> byCodeList = tytUserRecordService.getIndividuationSettings(1000001192L, codes);
        System.out.println(byCodeList);
    }

    @Resource(name = "userTelService")
    private UserTelService userTelService;

    @Test
    public void updateCellPhoneTest(){
        userCellPhoneChangeService.updateCellPhone(1000000573L,"15373025539","13552955572");
    }



    @Test
    public void tt(){
        List<UserTelModel> latestTelsByUserId = userTelService.getLatestTelsByUserId(1002001101L, null);
        System.out.println("castExcept1:{"+JSON.toJSONString(latestTelsByUserId)+"}");
        if(CollUtil.isNotEmpty(latestTelsByUserId)){
            System.out.println("castExcept1:{"+JSON.toJSONString(latestTelsByUserId.get(0))+"}");
            UserTelModel userTel = latestTelsByUserId.get(0);
            System.out.println("98uufje98wehj");
//            if(Objects.nonNull(userTel.getCreateTime()) && userTel.getCreateTime().compareTo(monthFirstDay) >0 && userTel.getCreateTime().compareTo(monthLastDay)<0){
//                return;
//            }
        }
    }
}
