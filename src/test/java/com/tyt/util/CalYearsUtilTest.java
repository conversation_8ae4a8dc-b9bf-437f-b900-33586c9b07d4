package com.tyt.util;

import org.junit.Assert;
import org.junit.Test;

import java.time.LocalDate;

public class CalYearsUtilTest {

    @Test
    public void calculateYear() {
//        int years = CalYearsUtil.calculatePeriod(LocalDate.parse("2008-10-11 18:01:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")), LocalDate.now());
        String period = CalYearsUtil.calculatePeriod("2017-03-19 14:04:33", LocalDate.now());
        Assert.assertEquals(period, "1年9月");
    }


}