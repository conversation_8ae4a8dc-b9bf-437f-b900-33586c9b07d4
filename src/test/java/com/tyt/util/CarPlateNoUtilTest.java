package com.tyt.util;

import org.junit.Test;

import static org.junit.Assert.assertEquals;

/**
 * @ClassName CarPlateNoUtil
 * @Description
 * 常规车牌号：省份+地区代码+五位数字/大写英文字母（序号位）如：粤B12345。
 * 1. 序号位不存在字母I和O防止1、0混淆
 * 2. 省份范围：京、津、沪、渝、冀、豫、云、辽、黑、湘、皖、鲁、新、苏、浙、赣、鄂、桂、甘、晋、蒙、陕、吉、闽、贵、粤、青、藏、川、宁、琼。
 * 3. 地区代码O为省级公安厅专用车牌
 * 4. 地区代码U为省级政府专用车牌
 * 5. 地区代码中暂无I
 *
 * 新能源车牌号：省份简称（1位汉字）+发牌机关代号（1位字母）+序号（6位）
 * 1. 小型新能源汽车号牌（序号）的第一位必须使用字母D、F（D代表纯电动新能源汽车，F代表非纯电动新能源汽车），第二位可以使用字母或者数字，后四位必须使用数字。
 * 2. 大型新能源汽车号牌（序号）的第六位必须使用字母D、F（D代表纯电动新能源汽车，F代表非纯电动新能源汽车），前五位必须使用数字。
 * 3. 序号中英文字母I和O不能使用。
 * 4. 省份范围同常规车牌号
 * 5. 发牌机关代码暂无I
 *
 * 警车车牌：车牌最后汉字为警字
 * 1. 省份+地区代码+4位数字+警（川A0001警）
 * 2. 省份+地区代码+字母+3位数字（川AA001警）字母可选项包括（A、B、C、D）
 * 3. 省份范围同常规车牌号
 * 4. 地区代码没有I
 * 5. 地区代码为O时代表为省级公安厅专用车牌
 *
 * 领事馆车牌：车牌中包括“使”或“领”字
 * 1. 大使馆：三位国家代码（数字）+三位车辆编号（数字）+使
 * 2. 领事馆：省份简称+地区代码+四位车辆编号（数字）+领（省份与地区代码可选范围包括：沪A、粤A、川A、云A、桂A、鄂A、闽D、鲁B、陕A、蒙A、蒙E、蒙H、藏A、黑A、辽A、渝A）
 *
 * 武警车牌：车牌开头包括WJ
 * 1. 武警总部车牌：WJ+•（中间点）+四个数字+数字或字母
 * 2. 武警地方车牌：WJ+省份简称+四位数字+数字或字母
 * 3. 省份范围同常规车牌号
 * 4. 其中字母包括（T D S H B X J）
 *
 * 军用车牌：字头+字头号 +序号组成。
 * 1. 字头：大写字母汉语拼音字母，字母包括（VKHBSLJNGCE）
 * 2. 字头号：大写英文字母，字母包括（A-D,J-P,R-T,V,Y）
 * 3. 序号：5位数字
 * <AUTHOR>
 * @Date 2019-03-14 15:31
 * @Version 1.0
 */

public class CarPlateNoUtilTest {

    @Test
    public void test() {
        CarPlateNoUtil t = new CarPlateNoUtil();

        //常规车牌号
        assertEquals(true,t.checkPlateNumberFormat("粤B12345"));
        assertEquals(true,t.checkPlateNumberFormat("粤O12345"));
        assertEquals(true,t.checkPlateNumberFormat("粤O12AB5"));
        assertEquals(false,t.checkPlateNumberFormat("粤BI2345"));
        assertEquals(false,t.checkPlateNumberFormat("粤BO2345"));
        assertEquals(false,t.checkPlateNumberFormat("粤Ba2345"));
        assertEquals(false,t.checkPlateNumberFormat("粤I12345"));
        assertEquals(false,t.checkPlateNumberFormat("粤B123AD5"));
        assertEquals(false,t.checkPlateNumberFormat("的B1AB45"));
        //新能源车牌号
        assertEquals(true,t.checkPlateNumberFormat("京AD68059"));
        assertEquals(true,t.checkPlateNumberFormat("粤B12345F"));
        assertEquals(true,t.checkPlateNumberFormat("粤BDS2345"));
        assertEquals(false,t.checkPlateNumberFormat("粤B212345"));
        assertEquals(false,t.checkPlateNumberFormat("粤粤BD12345"));
        assertEquals(false,t.checkPlateNumberFormat("粤BDO2345"));
        assertEquals(false,t.checkPlateNumberFormat("粤BDI2345"));
        assertEquals(false,t.checkPlateNumberFormat("粤BA12345"));
        assertEquals(false,t.checkPlateNumberFormat("粤BD1234F"));
        assertEquals(false,t.checkPlateNumberFormat("粤B1D234F"));
        assertEquals(false,t.checkPlateNumberFormat("粤ID12345"));
        //警车
        assertEquals(true,t.checkPlateNumberFormat("粤B1234警"));
        assertEquals(true,t.checkPlateNumberFormat("粤BA234警"));
        assertEquals(true,t.checkPlateNumberFormat("粤BD234警"));
        assertEquals(false,t.checkPlateNumberFormat("粤B12G4警"));
        assertEquals(false,t.checkPlateNumberFormat("粤BG234警"));
        assertEquals(false,t.checkPlateNumberFormat("粤BI234警"));
        assertEquals(false,t.checkPlateNumberFormat("粤I1234警"));
        assertEquals(false,t.checkPlateNumberFormat("粤I1234"));
        assertEquals(false,t.checkPlateNumberFormat("粤B12345警"));
        assertEquals(false,t.checkPlateNumberFormat("粤B1235警警"));
        //领事馆
        assertEquals(true,t.checkPlateNumberFormat("123456使"));
        assertEquals(true,t.checkPlateNumberFormat("蒙H3456领"));
        assertEquals(true,t.checkPlateNumberFormat("闽D5965领"));
        assertEquals(false,t.checkPlateNumberFormat("使23456"));
        assertEquals(false,t.checkPlateNumberFormat("A23456使"));
        assertEquals(false,t.checkPlateNumberFormat("使123456"));
        assertEquals(false,t.checkPlateNumberFormat("川B2345领"));
        assertEquals(false,t.checkPlateNumberFormat("蒙H3S56领"));
        assertEquals(false,t.checkPlateNumberFormat("蒙H356领"));
        assertEquals(false,t.checkPlateNumberFormat("豫A4567领"));
        //武警车牌
        assertEquals(true,t.checkPlateNumberFormat("WJ·1234T"));
        assertEquals(true,t.checkPlateNumberFormat("WJ·12345"));
        assertEquals(true,t.checkPlateNumberFormat("WJ•1234T"));
        assertEquals(true,t.checkPlateNumberFormat("WJ豫1234T"));
        assertEquals(true,t.checkPlateNumberFormat("WJ豫12349"));
        assertEquals(false,t.checkPlateNumberFormat("WJ豫1234A"));
        assertEquals(false,t.checkPlateNumberFormat("WJ·1234G"));
        assertEquals(false,t.checkPlateNumberFormat("WJ豫12346T"));
        assertEquals(false,t.checkPlateNumberFormat("WJ豫12346T"));
        assertEquals(false,t.checkPlateNumberFormat("WJ豫12346T"));
        assertEquals(false,t.checkPlateNumberFormat("WJ·12346T"));
        assertEquals(false,t.checkPlateNumberFormat("wj·12345"));
        assertEquals(false,t.checkPlateNumberFormat("WJ.12345"));
        assertEquals(false,t.checkPlateNumberFormat("WJ·A234O"));
        assertEquals(false,t.checkPlateNumberFormat("WJ·1234O"));
        //军用车牌
        assertEquals(true,t.checkPlateNumberFormat("KA12345"));
        assertEquals(true,t.checkPlateNumberFormat("NS56862"));
        assertEquals(true,t.checkPlateNumberFormat("HV12345"));
        assertEquals(false,t.checkPlateNumberFormat("KAB2345"));
        assertEquals(false,t.checkPlateNumberFormat("KE12345"));
        assertEquals(false,t.checkPlateNumberFormat("AA12345"));
        assertEquals(false,t.checkPlateNumberFormat("HV123456"));

        assertEquals(true,t.checkPlateNumberFormat("A999999"));

    }

}
