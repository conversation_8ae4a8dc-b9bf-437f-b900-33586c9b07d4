package com.tyt.util;

import com.tyt.plat.test.base.TytTestBase;
import org.junit.Test;

import java.net.URI;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2022/4/20 10:57
 */
public class LoadDicTest {

    @Test
    public void loadDicTest() {
        URI dict_file;
        //获取资源词典文件
        try {
            dict_file = GoodsUnique.class.getResource("goods.dic").toURI();
        } catch (Exception ex) {
            ex.printStackTrace();
            return;
        }
        System.out.println("dict_file="+dict_file);
        Path dict_path = Paths.get(dict_file);

        System.out.println("path=" + dict_path.toString());

        System.out.println("finished ... ");
    }
}