package com.tyt.util;

import junit.framework.TestCase;
import org.apache.commons.lang3.math.NumberUtils;
import org.junit.Test;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/8/18 13:37
 */
public class StringUtilTest extends TestCase {

    @Test
    public void testIsDouble() {

        String text = "22.3";

        boolean number = NumberUtils.isNumber(text);

        boolean aDouble = StringUtil.isDouble(text);

        System.out.println("finished ... ");

    }

    @Test
    public void testSplitAllNumber() {

        String text1 = "123123.45,123a23.45,123 ";
        String text2 = "123123.45,12323,123";
        String text3 = "123,1a1,456";
        String text4 = "123,154,456";

        boolean b1 = StringUtil.splitAllNumber(text1, ",");
        boolean b2 = StringUtil.splitAllNumber(text2, ",");
        boolean b3 = StringUtil.splitAllNumber(text3, ",");
        boolean b4 = StringUtil.splitAllNumber(text4, ",");

        boolean c1 = StringUtil.splitAllDigits(text1, ",");
        boolean c2 = StringUtil.splitAllDigits(text2, ",");
        boolean c3 = StringUtil.splitAllDigits(text3, ",");
        boolean c4 = StringUtil.splitAllDigits(text4, ",");

        System.out.println("finished ... ");
    }

    public static void integerTest(){
        String s = "";

        Integer a = Integer.parseInt(s);
        System.out.println("finished ... ");
    }

    public static void main(String[] args) {

        String num = "0.1";

        double v = Double.parseDouble(num);

        boolean number = NumberUtils.isNumber(num);

        integerTest();

    }

}