package com.tyt.util;

import com.tyt.messagecenter.core.utils.CommonUtil;
import com.tyt.plat.test.base.TytTestBase;
import org.junit.Test;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2022/4/20 10:57
 */
public class GoodsUniqueTest extends TytTestBase {


    private void contentTest(String taskContent){

        String keyInfo = GoodsUnique.extractKeyInfo(taskContent);

        System.out.println(taskContent + " : " + keyInfo);

        System.out.println("=============================\n");
    }

    @Test
    public void extractKeyInfo() {

        String taskContent = "小松200挖掘机q";
        taskContent = GoodsUnique.extractKeyInfo(taskContent);

        String oneContent = "小松200挖掘机+斗测试";

        CommonUtil.loopSleep(1000, 20, "test_main_sleep");

        for(int i=0; i<10000;i++){

            this.contentTest(oneContent);

            CommonUtil.loopSleep(1000, 10, "test_main_sleep");
        }

        CommonUtil.loopSleep(1000, 30);
        System.out.println("finished ... ");
    }
}