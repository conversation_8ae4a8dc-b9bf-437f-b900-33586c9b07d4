package com.tyt.receive;

import com.alibaba.fastjson.JSONObject;
import com.tyt.model.ResultMsgBean;
import com.tyt.plat.test.base.TytTestBase;
import com.tyt.receive.service.TransportBackendService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.InvocationTargetException;
@Slf4j
public class TransportBackendServiceTest extends TytTestBase {

    @Autowired
    private TransportBackendService transportBackendService;

    @Test
    public void getOwnerGoodsDetailTest() throws InvocationTargetException, IllegalAccessException {
        ResultMsgBean ownerGoodsDetail = transportBackendService.getOwnerGoodsDetail(1000000645L, 37884701L, 0);
        log.info(JSONObject.toJSONString(ownerGoodsDetail));
    }
}
