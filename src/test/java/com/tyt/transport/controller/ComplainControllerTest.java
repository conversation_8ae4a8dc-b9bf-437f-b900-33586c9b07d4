package com.tyt.transport.controller;

import com.tyt.infofee.bean.TransportOrdersListBean;
import com.tyt.model.Car;
import com.tyt.model.CarDetailTail;
import com.tyt.model.ResultMsgBean;
import com.tyt.plat.test.base.TytTestBase;
import com.tyt.transport.bean.ComplaintRecordBean;
import com.tyt.user.service.CarService;
import com.tyt.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class ComplainControllerTest extends TytTestBase {

    @Autowired
    private ComplaintController complaintController;

    @Autowired
    private CarService carService;

    @Test
    public void testSave() {
        ComplaintRecordBean recordBean = new ComplaintRecordBean();
        recordBean.setMsgId(78830279L);
        recordBean.setRespondentId(147306L);
        recordBean.setReason("6290测试");
        recordBean.setUserId(1000000638L);

        ResultMsgBean resultMsgBean = complaintController.save(recordBean, "11");
        log.info(String.valueOf(resultMsgBean));

        resultMsgBean = complaintController.save(recordBean, "10");
        log.info(String.valueOf(resultMsgBean));

        resultMsgBean = complaintController.save(recordBean, null);
        log.info(String.valueOf(resultMsgBean));
    }

}
