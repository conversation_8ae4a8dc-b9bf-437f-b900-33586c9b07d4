package com.tyt.transport.service;

import com.tyt.messagecenter.core.utils.DateUtil;
import com.tyt.model.Transport;
import com.tyt.model.TransportMain;
import com.tyt.plat.test.base.TytTestBase;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/12/26 15:56
 */
public class TransportMainServiceTest extends TytTestBase {

    @Autowired
    private TransportMainService transportMainService;

    @Autowired
    private TransportService transportService;

    @Test
    public void testisCashPrizeActivityTransport() {
        Long tsId = 88823075L;

        TransportMain byId = transportMainService.getById(tsId);
        boolean cashPrizeActivityTransport = transportMainService.isCashPrizeActivityTransport(byId);
        System.out.println(cashPrizeActivityTransport);
    }

    @Test
    public void testAddTransportMain() {

        Long tsId = 78829796L;

        TransportMain byId = transportMainService.getById(tsId);

        String startLatitude = byId.getStartLatitude();

        byId.setTaskContent("测试字段。。。");
        transportMainService.update(byId);

        TransportMain transportMainForId = transportMainService.getTransportMainForId(tsId);

        TransportMain bySrcMsgId = transportMainService.getBySrcMsgId(tsId);

        System.out.println("finished ... ");
    }


    @Test
    public void testTransport() {

        Date nowTime = new Date();
        String dayStr = DateUtil.dateToString(nowTime, DateUtil.date_time_format);

        Long tsId = 44109737L;

        Transport transport = transportService.getById(tsId);

        transport.setTaskContent("测试字段-ts。。。");

        String startLatitude = transport.getStartLatitude();

        transportService.update(transport);

        Long srcMsgId = transport.getSrcMsgId();

        List<Long> tsIdList = new ArrayList<>();
        tsIdList.add(tsId);

        List<Transport> tsList = transportService.queryListByGoodIds(tsIdList);
        Transport byGoodId = tsList.get(0);

        Transport lastBySrcMygId = transportService.getLastBySrcMygId(srcMsgId);

        System.out.println("finished ... ");
    }

    @Test
    public void testTransportprice() {

        Long userId = 1000000305L;
        Date publishTime = DateUtils.addMonths(new Date(), -2);

        List<String> priceList = transportMainService.selectOfPublishType(userId, publishTime);
        if (CollectionUtils.isEmpty(priceList)) {
            System.out.println(1);
        } else {
            priceList = priceList.stream().filter( StringUtils::isNotBlank).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(priceList)) {
                System.out.println(0);
            } else {
                System.out.println(1);
            }
        }
    }





}