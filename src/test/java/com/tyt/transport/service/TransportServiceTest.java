package com.tyt.transport.service;

import com.tyt.callPhoneRecord.service.CallPhoneRecordService;
import com.tyt.infofee.bean.SingleGoodsDetailBean;
import com.tyt.infofee.controller.TransportGoodsController;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.Transport;
import com.tyt.model.TransportMain;
import com.tyt.model.UserPermission;
import com.tyt.permission.service.UserPermissionService;
import com.tyt.plat.entity.base.TytTransportTecServiceFee;
import com.tyt.plat.mapper.base.TytTransportTecServiceFeeMapper;
import com.tyt.plat.test.base.TytTestBase;
import com.tyt.plat.vo.ts.CallLogQuery;
import com.tyt.plat.vo.ts.MeetCommissionRulesResult;
import com.tyt.plat.vo.ts.TransportLabelJson;
import com.tyt.transport.querybean.CallLogQueryResultBean;
import com.tyt.transport.service.impl.TransportBusiness;
import com.tyt.transport.vo.TytTecServiceFeeConfigToComputResult;
import com.tyt.user.service.TytConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2023/3/17 12:20
 */
@Slf4j
public class TransportServiceTest extends TytTestBase {

    @Autowired
    private TransportService transportService;

    @Autowired
    private TransportMainService transportMainService;

    @Autowired
    private BsPublishTransportService bsPublishTransportService;

    @Autowired
    private UserPermissionService userPermissionService;

    @Autowired
    private TransportBusiness transportBusiness;

    @Autowired
    private TytTransportTecServiceFeeMapper tytTransportTecServiceFeeMapper;

    @Autowired
    private TransportGoodsController transportGoodsController;

    @Autowired
    private TytConfigService tytConfigService;

    @Autowired
    private CallPhoneRecordService callPhoneRecordService;

    @Autowired
    private TytTransportIntentionService tytTransportIntentionService;



    @Test
    public void testGetCallLog() {
        Long userId = 1000000453L;

        String phoneReq = "182102413401";

        CallLogQuery callLogQuery = new CallLogQuery();
        callLogQuery.setUserId(userId);
        callLogQuery.setCurrentPage(1);

        CallLogQueryResultBean callLog = transportService.getCallLog(callLogQuery);

        callLogQuery.setCellPhone(phoneReq);

        callLog = transportService.getCallLog(callLogQuery);

        System.out.println("finished ... ");

        assert callLog != null;
    }

    @Test
    public void testGetBottomIdSubtract() {

        for(int i=0; i<100; i++) {

            Long bottomIdSubtract = transportService.getBottomIdSubtract();
            System.out.println("finished ... ");
        }
        System.out.println("finished ... ");

    }

    @Test
    public void  testModifyQualityGoodSimilarCode(){
        log.info(transportService.modifyQualityGoodSimilarCode(transportService.getLastBySrcMygId(86722569L), "test"));
    }

    @Test
    public void test() {
        ArrayList<Long> srcMsgIds = new ArrayList<>();
        srcMsgIds.add(88822370L);
        for (Long srcMsgId : srcMsgIds) {
            TransportMain transportMain = transportMainService.getTransportMainForId(srcMsgId);
            Transport tytTransport = new Transport();
            BeanUtils.copyProperties(transportMain, tytTransport);
            MeetCommissionRulesResult meetCommissionRules = new MeetCommissionRulesResult();
            TytTecServiceFeeConfigToComputResult tytTecServiceFeeConfigToComputResult = bsPublishTransportService.computeTecServiceFeeBtTransportData(tytTransport, null, meetCommissionRules, false);
            bsPublishTransportService.saveCommissionTransportTecServiceFeeData(tytTransport.getSrcMsgId(), tytTecServiceFeeConfigToComputResult);
            System.out.println(1);
        }
    }

    @Test
    public void test4234() {
        ResultMsgBean tecServiceFeeByCarUserAndSrcMsgId = bsPublishTransportService.getTecServiceFeeByCarUserAndSrcMsgId(1000000802L, 86744803L);
        System.out.println(1);
    }

    @Test
    public void test2324() {
        transportGoodsController.getSingleDetail(1000000571L, 86744608L, 1, "586460", 1, "", 1,null,null,null,null,null, null,null,null,null, new MockHttpServletRequest(), new MockHttpServletResponse(), 10001L);
    }

    @Test
    public void test432() {
        callPhoneRecordService.asrCreate(86750696L);
    }

    @Test
    public void testIntention() {
        tytTransportIntentionService.disableIntentionTransport(88821381L);
    }

}