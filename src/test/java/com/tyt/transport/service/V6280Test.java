package com.tyt.transport.service;

import com.tyt.base.bean.BaseParameter;
import com.tyt.infofee.controller.TransportWayBillExController;
import com.tyt.model.ResultMsgBean;
import com.tyt.plat.test.base.TytTestBase;
import com.tyt.transport.controller.TransportController;
import com.tyt.user.controller.BlockInfoController;
import com.tyt.user.controller.UserController;
import com.tyt.user.service.TytConfigService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class V6280Test extends TytTestBase {

    @Autowired
    private TransportController transportController;
    @Autowired
    private UserController userController;
    @Autowired
    private BlockInfoController blockInfoController;
    @Autowired
    private TransportWayBillExController transportWayBillExController;

    @Autowired
    private TytConfigService tytConfigService;

    @Test
    public void getDetailRecommendListTest() {
        BaseParameter baseParameter = new BaseParameter();
        baseParameter.setUserId(1002000791L);
        ResultMsgBean resultMsgBean = transportController.getDetailRecommendList(baseParameter, 1L,1,30);
        log.info("{}", resultMsgBean);
    }

    @Test
    public void getAdviceTypesTest() {
        BaseParameter baseParameter = new BaseParameter();
        baseParameter.setClientSign("21");
        log.info("{}", userController.getAdviceTypes(baseParameter));
        baseParameter.setClientSign("22");
        log.info("{}", userController.getAdviceTypes(baseParameter));
        baseParameter.setClientSign("0");
        log.info("{}", userController.getAdviceTypes(baseParameter));
    }

    @Test
    public void getReasonsTest() {
        BaseParameter baseParameter = new BaseParameter();
        log.info("{}", blockInfoController.getReasons(baseParameter));
    }

    @Test
    public void saveAdvice6280Test() {
        BaseParameter baseParameter = new BaseParameter();
        baseParameter.setUserId(1002000791L);
        baseParameter.setClientSign("21");
        baseParameter.setClientVersion("6280");

        log.info("{}", userController.saveAdvice6280(baseParameter, "内容200字", null,
                "发货不方便：发货流程复杂/填写内容太多/必填内容无法填写等"));
    }

    @Test
    public void getMyComplaintListTest() {
        BaseParameter baseParameter = new BaseParameter();
        baseParameter.setUserId(1002000055L);
        baseParameter.setClientSign("21");
        baseParameter.setClientVersion("6280");
        ResultMsgBean myComplaintList = transportWayBillExController.getMyComplaintList(baseParameter, 2,
                11);
        log.info("{}", myComplaintList);
    }

    @Test
    public void test2344() {
        Integer expireTimeConfig = tytConfigService.fetchRecommendIntValue("T_PRIVACY_PHONE_NUM_EXPIRE_TIME", 3);
        int expireTimeSec = expireTimeConfig * 60 * 60;
        System.out.println(1);
    }


}
