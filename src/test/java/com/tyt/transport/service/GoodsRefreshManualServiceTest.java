package com.tyt.transport.service;

import com.tyt.model.Transport;
import com.tyt.plat.test.base.TytTestBase;
import com.tyt.plat.vo.ts.SaveDirectReq;
import com.tyt.plat.vo.ts.TransportUpdateDataReq;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2023/3/17 12:20
 */
@Slf4j
public class GoodsRefreshManualServiceTest extends TytTestBase {

    @Autowired
    private TransportService transportService;

    @Autowired
    private GoodsRefreshManualService goodsRefreshManualService;


    @Test
    public void testAddGoodsRefresh() {
        Transport transport = transportService.getById(215280877L);
        Transport originalTransport = null;
        SaveDirectReq saveDirectReq = new SaveDirectReq();
        saveDirectReq.setClientSign("1");
        saveDirectReq.setPriceSuggest("100");
        TransportUpdateDataReq transportUpdateDataReq = new TransportUpdateDataReq();
        transportUpdateDataReq.setSourcePage(2);
        saveDirectReq.setUpdateDataReq(transportUpdateDataReq);

        goodsRefreshManualService.addGoodsRefresh(transport, saveDirectReq, originalTransport);
    }
}