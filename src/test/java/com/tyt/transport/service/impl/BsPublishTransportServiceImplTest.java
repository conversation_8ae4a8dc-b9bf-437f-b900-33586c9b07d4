package com.tyt.transport.service.impl;

import com.tyt.messagecenter.core.utils.CommonUtil;
import com.tyt.model.ResultMsgBean;
import com.tyt.plat.entity.base.TytSpecialCarPriceConfig;
import com.tyt.plat.test.base.TytTestBase;
import com.tyt.transport.querybean.CalculatePriceBean;
import com.tyt.transport.querybean.CargoOwnerQueryBean;
import com.tyt.transport.querybean.TransportPublishBean;
import com.tyt.transport.service.BsPublishTransportService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2022/4/27 13:57
 */
@Slf4j
public class BsPublishTransportServiceImplTest extends TytTestBase {

    @Autowired
    private BsPublishTransportServiceImpl transportService;

    @Test
    public void regCheckTest() {
        String reg = "(?<!\\d)17\\.5米(?!吨)";

        String content = "1a17.5米";
        boolean b = CommonUtil.regCheck(reg, content);

        System.out.println(b);

        Pattern pattern = Pattern.compile(reg);
        Matcher matcher = pattern.matcher(content);

        boolean result = matcher.find();

        System.out.println(result);

        System.out.println("finished ... ");
    }

    @Test
    public void isExclusive() {

        String[] contentArray = {
                "xx17米5米吨以上吨xx测试",
                "xx17米5吨以上吨xx测试",
                "xx17.5测试",
                "xx17.5以上测试",
                "xx17.52米",
                "xx117.5米",
                "xx17米5米",
                "xx17米5米吨测试",
                "xx17米5米以上测试",
                "17米5",
                "xx17米5吨测试",
                "17吨5",
        };

        for(String oneContent : contentArray) {
            TransportPublishBean publishBean = new TransportPublishBean();

            publishBean.setTaskContent(oneContent);

            BsPublishTransportServiceImpl transportService = new BsPublishTransportServiceImpl();

            boolean exclusive = transportService.isExclusive(publishBean);

            System.out.println(oneContent + " : " + exclusive);
        }

        System.out.println("finished ... ");

    }

    @Test
    public void isShowCargoOwner() {
        CargoOwnerQueryBean queryBean = new CargoOwnerQueryBean();
        queryBean.setUserId(1002000780L);
        ResultMsgBean showCargoOwner = transportService.isShowCargoOwner(queryBean);
        log.info("result：{}", showCargoOwner);
    }

    @Test
    public void calculatePriceWithPriceRule() {
        CalculatePriceBean req = new CalculatePriceBean();
        req.setStartCity("保定市");
        req.setDestCity("北京市");
        req.setWeight("20");
        req.setOtherFee(new BigDecimal("100"));
        req.setDriverDriving(1);

        TytSpecialCarPriceConfig config = new TytSpecialCarPriceConfig();
        config.setDrivingFee(new BigDecimal("100"));

        req.setDistanceKilometer(new BigDecimal("201"));
        config.setPriceRule("[{\"start\":\"0\",\"end\":200,\"type\":2,\"price\":10},{\"start\":\"200\",\"end\":300,\"type\":1,\"price\":10}]");

        // req.setDistanceKilometer(new BigDecimal("30"));
        // req.setDistanceKilometer(new BigDecimal("130"));
        // req.setDistanceKilometer(new BigDecimal("400"));
        // config.setPriceRule("[{\"start\":\"100\",\"end\":200,\"type\":1,\"price\":410},{\"start\":\"200\",\"end\":300,\"type\":2,\"price\":10},{\"start\":\"300\",\"end\":999999,\"type\":2,\"price\":10}]");

        // req.setDistanceKilometer(new BigDecimal("100"));
        // req.setDistanceKilometer(new BigDecimal("150"));
        // req.setDistanceKilometer(new BigDecimal("300"));
        // config.setPriceRule("[{\"start\":\"100\",\"end\":200,\"type\":1,\"price\":410},{\"start\":\"200\",\"end\":300,\"type\":2,\"price\":10}]");

        // req.setDistanceKilometer(new BigDecimal("10"));
        // req.setDistanceKilometer(new BigDecimal("100"));
        // req.setDistanceKilometer(new BigDecimal("150"));
        // req.setDistanceKilometer(new BigDecimal("200"));
        // req.setDistanceKilometer(new BigDecimal("250"));
        // config.setPriceRule("[{\"start\":\"100\",\"end\":200,\"type\":2,\"price\":10}]");

        // req.setDistanceKilometer(new BigDecimal("10"));
        // req.setDistanceKilometer(new BigDecimal("100"));
        // req.setDistanceKilometer(new BigDecimal("150"));
        // req.setDistanceKilometer(new BigDecimal("200"));
        // req.setDistanceKilometer(new BigDecimal("250"));
        // config.setPriceRule("[\"start\":\"100\",\"end\":200,\"type\":1,\"price\":410}]");
        BigDecimal bigDecimal = transportService.calculatePriceWithPriceRule(req, config);
        log.info("result:{}", bigDecimal);
    }

    @Test
    public void getDepositAndRefundType() {
        // Long userId = 1002000056L;
        // Long userId = 10L;
        Long userId = 4057L;
        ResultMsgBean depositAndRefundType = transportService.getDepositAndRefundType(userId, null);
        log.info("result:{}", depositAndRefundType.toString());
    }

    @Test
    public void getScreeningWordCheck() {

        String text = "中联50玉米收割机五线十轴1;";

        ResultMsgBean screeningWordCheck = transportService.getScreeningWordCheck("", text);

        System.out.println("finished ... ");

    }
}