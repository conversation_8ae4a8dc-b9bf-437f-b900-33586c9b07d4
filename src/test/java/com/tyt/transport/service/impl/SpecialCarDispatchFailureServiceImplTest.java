package com.tyt.transport.service.impl;

import com.tyt.model.TransportMain;
import com.tyt.plat.test.base.TytTestBase;
import com.tyt.transport.service.SpecialCarDispatchFailureService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @since 2024-08-12 17:44
 */
public class SpecialCarDispatchFailureServiceImplTest extends TytTestBase {

    @Autowired
    private SpecialCarDispatchFailureService specialCarDispatchFailureService;

    @Test
    public void processWorkOrderStatus() {
        TransportMain main = new TransportMain();
        main.setSrcMsgId(88820688L);
        specialCarDispatchFailureService.processWorkOrderStatus(main);
    }
}