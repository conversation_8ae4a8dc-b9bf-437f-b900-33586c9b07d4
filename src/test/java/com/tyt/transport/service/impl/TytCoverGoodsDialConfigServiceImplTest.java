package com.tyt.transport.service.impl;

import com.alibaba.fastjson.JSON;
import com.tyt.infofee.bean.GoodsSingleDetailResultBean;
import com.tyt.model.TransportMain;
import com.tyt.plat.test.base.TytTestBase;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.transport.service.TransportMainService;
import com.tyt.transport.service.TytCoverGoodsDialConfigService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * <AUTHOR>
 * @since 2024/02/23 13:42
 */
public class TytCoverGoodsDialConfigServiceImplTest  extends TytTestBase {

    private static final String COVER_GOODS_DIAL_CONFIG_N_TIMES_KEY = "CoverGoodsDialConfigNTimes";

    @Autowired
    private TytCoverGoodsDialConfigService tytCoverGoodsDialConfigService;
    @Autowired
    private TransportMainService transportService;

    @Test
    public void selectXTime() {
        String dialConfigNLeft = RedisUtil.getMapValue(COVER_GOODS_DIAL_CONFIG_N_TIMES_KEY, String.valueOf(1002000807));
        System.out.println("dialConfigNLeft===="+dialConfigNLeft);
    }

    @Test
    public void coverGoodsVersion4() {
        //获取运输信息表基本信息
        TransportMain transportMain = transportService.getTransportMainForId(88820701L);
        GoodsSingleDetailResultBean.CoverGoodsDialInfo coverGoodsDialInfo =
                tytCoverGoodsDialConfigService.coverGoodsVersion4(1002001059L, transportMain);
        System.out.println(JSON.toJSONString(coverGoodsDialInfo));
    }
}