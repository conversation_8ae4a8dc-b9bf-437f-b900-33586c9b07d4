package com.tyt.transport.service;

import com.tyt.model.User;
import com.tyt.plat.test.base.TytTestBase;
import com.tyt.transport.service.impl.TransportBusiness;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * TransportBusinessTest
 *
 * <AUTHOR>
 * @since 2024-09-29 11:30
 */
public class TransportBusinessTest extends TytTestBase {
    @Resource
    private TransportBusiness transportBusiness;

    @Test
    public void autoAddCargoOwner() {
        User user = new User();
        user.setId(1002001035L);
        user.setCellPhone("***********");
        user.setUserName("test");
        transportBusiness.autoAddCargoOwner(1L , user);
    }
}
