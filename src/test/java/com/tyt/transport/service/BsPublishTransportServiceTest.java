package com.tyt.transport.service;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.tyt.infofee.bean.InfoFeeMyPublishGoodsResultBean;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.Transport;
import com.tyt.model.TransportMain;
import com.tyt.plat.client.trade.infofee.ApiTradeInfoFeeClient;
import com.tyt.plat.client.trade.infofee.dto.UserPerformanceNumDTO;
import com.tyt.plat.mapper.base.TytAppCallLogMapper;
import com.tyt.plat.test.base.TytTestBase;
import com.tyt.plat.vo.remote.CarryPriceVo;
import com.tyt.pricingRoute.service.PricingRouteConfigService;
import com.tyt.transport.bean.CommissionTypeBean;
import com.tyt.transport.bean.DrawCommissionReq;
import com.tyt.transport.enums.YesOrNoEnum;
import com.tyt.transport.querybean.CheckGoodCarPriceTransportBean;
import com.tyt.transport.querybean.SameTransportAveragePriceReq;
import com.tyt.transport.querybean.TransportCarryBean;
import com.tyt.transport.querybean.TransportPublishBean;
import com.tyt.transportquotedprice.bean.TransportQuotedPriceTabDataVO;
import com.tyt.transportquotedprice.service.TransportQuotedPriceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import retrofit2.Response;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/8/24 10:22
 */
@Slf4j
public class BsPublishTransportServiceTest extends TytTestBase {

    @Autowired
    private BsPublishTransportService publishTransportService;

    @Autowired
    private TransportService transportService;

    @Autowired
    private FreightDetailService freightDetailService;

    @Autowired
    private TytAppCallLogMapper tytAppCallLogMapper;

    @Autowired
    private TransportMainService transportMainService;

    @Autowired
    private PricingRouteConfigService pricingRouteConfigService;

    @Autowired
    ApiTradeInfoFeeClient apiTradeInfoFeeClient;


    @Autowired
    private ExcellentPriceConfigService excellentPriceConfigService;

    @Autowired
    private TransportQuotedPriceService transportQuotedPriceService;

    @Test
    public void testPricingRoute() {
        String startProvince = "新疆维吾尔自治区";
        String startCity = "吐鲁番市";
        String destProvince = "西藏自治区";
        String destCity = "昌都市";
        boolean b = pricingRouteConfigService.checkExcellentGoodsPricingRoute(startProvince, startCity, destProvince, destCity);
        System.out.println(b);
    }

    @Test
    public void testTradeService() {
        // 判断货主是否履约过
        UserPerformanceNumDTO userPerformanceNumDTO = new UserPerformanceNumDTO();
        userPerformanceNumDTO.setUserId(1002000780L);
        // 1:车主  2：货主
        userPerformanceNumDTO.setUserType(2);
        userPerformanceNumDTO.setRiskOrderFlag(YesOrNoEnum.Y.getCode());
        try {
            log.info("调用trade_service获取用户成交单数开始,req:{}", JSONUtil.toJsonStr(userPerformanceNumDTO));
            Response<Integer> response = apiTradeInfoFeeClient.getUserPerformanceNum(userPerformanceNumDTO).execute();
            log.info("调用trade_service获取用户成交单数结束,response:{}", response.toString());
            Integer body = response.body();
            log.info("调用trade_service获取用户成交单数结束,response:{}",JSON.toJSONString(body));
        } catch (IOException e) {
            log.error("调用履约接口失败,请求参数：{}", JSONUtil.toJsonStr(userPerformanceNumDTO),e);
        }

        System.out.println(11);
    }


    @Test
    public void test1122() {
        SameTransportAveragePriceReq sameTransportAveragePriceReq = new SameTransportAveragePriceReq();
        sameTransportAveragePriceReq.setGoodsWeight("99");
        sameTransportAveragePriceReq.setStartCity("北京市");
        sameTransportAveragePriceReq.setDestCity("洛阳市");
        sameTransportAveragePriceReq.setDistance(new BigDecimal(100));
        ResultMsgBean sameTransportAveragePrice = freightDetailService.getSameTransportAveragePrice(sameTransportAveragePriceReq);
        System.out.println(1);
    }

    @Test
    public void test43142() {
        InfoFeeMyPublishGoodsResultBean infoFeeMyPublishGoodsResultBean = new InfoFeeMyPublishGoodsResultBean();
        infoFeeMyPublishGoodsResultBean.setSrcMsgId(86744928L);
        infoFeeMyPublishGoodsResultBean.setPrice("375");
        List<InfoFeeMyPublishGoodsResultBean> infoFeeMyPublishGoodsResultBeans = new ArrayList<>();
        infoFeeMyPublishGoodsResultBeans.add(infoFeeMyPublishGoodsResultBean);
        makeAddMoneyButton(infoFeeMyPublishGoodsResultBeans);
        Integer addMoneyButtonStyle = infoFeeMyPublishGoodsResultBeans.get(0).getAddMoneyButtonStyle();
        System.out.println(1);
    }

    @Test
    public void testCheckExcellentGoods() {
        log.info(String.valueOf(Boolean.valueOf(publishTransportService.checkExcellentGoods(1, 1000000591L, 1))));
    }

    @Test
    public void test() {
        try {
            publishTransportService.checkYouchePublishRemainingCount(1L, 1);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    @Test
    public void testCheckSimilarExist() {

        for(int i=0; i<100; i++){

            Long srcMsgId = 62989160L;

            boolean similarExist = publishTransportService.checkSimilarExist(srcMsgId);

            srcMsgId = 62989109L;
            similarExist = publishTransportService.checkSimilarExist(srcMsgId);

            System.out.println("finished ... ");

        }
    }

    private void testStandardTransport(Long standardId, Long customId){

        //标准货源
        Transport standardTransport = transportService.getById(standardId);

        publishTransportService.setTransportStandardInfo(standardTransport);

        String standardHashCode = publishTransportService.getNewHashCode(standardTransport);

        //点选的
        Transport customTransport = transportService.getById(customId);
        customTransport.setMatchItemId(null);

        publishTransportService.setTransportStandardInfo(customTransport);

        String customHashCode = publishTransportService.getNewHashCode(customTransport);

        System.out.println("finished ... ");

    }

    @Test
    public void testSetTransportStandardInfo() {

        //飞机油罐车
        //标准货源
        Long standardId = 44115238L;

        //点选的
        Long customId = 44115239L;
        this.testStandardTransport(standardId, customId);

        //飞机加油车
        standardId = 44115241L;

        //点选的
        customId = 44115243L;
        this.testStandardTransport(standardId, customId);

        System.out.println("finished ... ");
    }

    @Test
    public void testFreightAddMoneyNum() throws Exception{

        String clientVersion = "6340";
        String clientSign = "32";

        Long srcMsgId = 88819939L;
        Long userId = 1000000453L;
        String addPrice = "500.5";

        publishTransportService.freightAddMoneyNum(userId, srcMsgId, addPrice, clientVersion, clientSign,
                0,0, null, 1);

        System.out.println("finished ... ");

    }

    @Test
    public void test22() {
        TransportPublishBean publishBean = new TransportPublishBean();
        publishBean.setStartProvinc("广东");
        publishBean.setStartCity("广州市");
        publishBean.setStartArea("荔湾区");
        publishBean.setDestProvinc("广东");
        publishBean.setDestCity("云浮市");
        publishBean.setDestArea("云城区");
        publishBean.setTaskContent("万通机械95挖掘机");
        publishBean.setWeight("8");
        publishBean.setLength("6.62");
        publishBean.setWide("2.2");
        publishBean.setHigh("2.93");
        publishBean.setDistance(new BigDecimal("151"));
        publishBean.setExcellentGoods(0);
        publishBean.setUserId(1000000566L);
        publishBean.setPublishType(2);
        publishBean.setPrice("1500");


        CheckGoodCarPriceTransportBean priceTransportBean = new CheckGoodCarPriceTransportBean(publishBean.getStartProvinc(), publishBean.getStartCity(), publishBean.getStartArea()
                , publishBean.getDestProvinc(), publishBean.getDestCity(), publishBean.getDestArea()
                , publishBean.getTaskContent(), publishBean.getWeight(), publishBean.getLength(), publishBean.getWide(), publishBean.getHigh()
                , publishBean.getDistance().toString(), publishBean.getExcellentGoods(), publishBean.getUserId(), publishBean.getPublishType(), publishBean.getPrice(), publishBean.getGoodTypeName());
        boolean b = publishTransportService.checkTransportIsGoodCarPriceTransport(priceTransportBean);
        System.out.println(1);
    }


    private void makeAddMoneyButton(List<InfoFeeMyPublishGoodsResultBean> goodsList) {
        for (InfoFeeMyPublishGoodsResultBean infoFeeMyPublishGoodsResultBean : goodsList) {
            if (org.apache.commons.lang3.StringUtils.isBlank(infoFeeMyPublishGoodsResultBean.getPrice()) || infoFeeMyPublishGoodsResultBean.getPrice().equals("0")) {
                continue;
            }

            Long tsId = infoFeeMyPublishGoodsResultBean.getSrcMsgId();

            TransportMain transportMainForId = transportMainService.getBySrcMsgId(tsId);

            Date lastAddMoneyTime = tytAppCallLogMapper.getLastAddMoneyTime(tsId);

            Date lastTime;
            if (lastAddMoneyTime == null && (transportMainForId == null || transportMainForId.getCtime() == null)) {
                lastTime = null;
            } else if (lastAddMoneyTime == null) {
                lastTime = transportMainForId.getCtime();
            } else if ((transportMainForId == null || transportMainForId.getCtime() == null) || lastAddMoneyTime.compareTo(transportMainForId.getCtime()) > 0) {
                lastTime = lastAddMoneyTime;
            } else {
                lastTime = transportMainForId.getCtime();
            }

            int viewLogCount = tytAppCallLogMapper.getViewLogCountBySrcMsgId(tsId);

            int callLogCount = tytAppCallLogMapper.getCallLogCountBySrcMsgId(tsId);

            long lastTimeDiff = 0;
            if (lastTime != null) {
                lastTimeDiff = Duration.between(lastTime.toInstant(), new Date().toInstant()).toMinutes();
            }


            if (transportMainForId.getPublishType() != null && transportMainForId.getPublishType() == 2) {
                //一口价
                if (lastTimeDiff >= 30 || viewLogCount > 10) {
                    infoFeeMyPublishGoodsResultBean.setAddMoneyButtonStyle(2);
                    continue;
                }
            } else {
                //电议有价
                if ((lastTimeDiff >= 30 || viewLogCount > 10) && callLogCount == 0) {
                    infoFeeMyPublishGoodsResultBean.setAddMoneyButtonStyle(2);
                    continue;
                }
            }
            infoFeeMyPublishGoodsResultBean.setAddMoneyButtonStyle(1);
        }
    }

    @Test
    public void testPricingConfig() {
        String startProvince = "北京";
        String startCity = "北京市";
        String destProvince = "湖北";
        String destCity = "武汉市";
        TransportCarryBean transportCarryBean = new TransportCarryBean();
        transportCarryBean.setStartProvince(startProvince);
        transportCarryBean.setStartCity(startCity);
        transportCarryBean.setDestProvince(destProvince);
        transportCarryBean.setDestCity(destCity);
        transportCarryBean.setDistance("1227");
        transportCarryBean.setGoodsWeight("3");
        transportCarryBean.setUserId(1002000780L);

        CarryPriceVo suggestPrice = excellentPriceConfigService.getSuggestPrice(transportCarryBean);
        System.out.println(JSONUtil.toJsonStr(suggestPrice));
    }

    @Test
    public void test432432() {
        TransportQuotedPriceTabDataVO transportQuotedPriceTabData = transportQuotedPriceService.getTransportQuotedPriceTabData(780L);
        ResultMsgBean resultMsgBean = ResultMsgBean.successResponse(transportQuotedPriceTabData);
        String jsonString = JSONObject.toJSONString(resultMsgBean);
        System.out.println(1);
    }


}