package com.tyt.common.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tyt.model.ResultMsgBean;
import com.tyt.plat.test.base.TytTestBase;
import com.tyt.transportquotedprice.bean.AllTransportQuotedPriceVo;
import com.tyt.transportquotedprice.service.TransportQuotedPriceService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/05/28 11:09
 */
public class WordBookControllerTest extends TytTestBase {

    @Autowired
    private WordBookController workBookController;

    @Autowired
    private TransportQuotedPriceService transportQuotedPriceService;

    @Test
    public void test() {
        ResultMsgBean list = workBookController.list(null);

        System.out.println(JSON.toJSONString(list));
    }

    @Test
    public void test4324() {
        List<AllTransportQuotedPriceVo> allPublishingTransportQuotedPriceList = transportQuotedPriceService.getAllPublishingTransportQuotedPriceList(1000001781L);
        String jsonString = JSONObject.toJSONString(allPublishingTransportQuotedPriceList);
        System.out.println(1);
    }
}
