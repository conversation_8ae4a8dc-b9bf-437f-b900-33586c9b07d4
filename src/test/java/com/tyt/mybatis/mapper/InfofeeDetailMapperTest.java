package com.tyt.mybatis.mapper;

import com.alibaba.fastjson.JSON;
import com.tyt.infofee.bean.BaseTransInfo;
import com.tyt.infofee.bean.CreditUserInfo;
import com.tyt.infofee.bean.ExceptionInfo;
import com.tyt.infofee.bean.InfoFeePayinfo;
import com.tyt.infofee.enums.ExPartyEnum;
import com.tyt.infofee.enums.InfofeeStatusEnum;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations={
        "classpath:/config/spring/spring-common.xml",
        "classpath:/config/spring/spring-tytrecommend.xml"})
public class InfofeeDetailMapperTest {

    @Autowired
    private InfofeeDetailMapper infofeeDetailMapper;

    @Test
    public void queryBaseTransInfo() {
        String tsOrderNo = "19010700000006";
        long srcMsgID = infofeeDetailMapper.querySrcMsgId(tsOrderNo);
        Assert.assertEquals(srcMsgID, 33783880);
        BaseTransInfo baseTransInfo = infofeeDetailMapper.queryBaseTransInfo(srcMsgID);
        Assert.assertEquals(baseTransInfo.getOwnerId(), "147945");
        Assert.assertNotNull(baseTransInfo.getTsId());
        Assert.assertNotNull(baseTransInfo.getOwnerName());
    }

    @Test
    public void queryPayInfoList() {
        String tsOrderNo = "19021800000046";
        List<InfoFeePayinfo> infoFeePayinfoList = infofeeDetailMapper.queryPayInfoList(tsOrderNo);
        Assert.assertEquals(infoFeePayinfoList.size(), 2);
    }

    @Test
    public void queryCreditUserInfo() {
        String payUserIds = "(147373,147943)";
        String userId = "148136";
        List<CreditUserInfo> userInfoList = infofeeDetailMapper.queryCreditUserInfo(payUserIds, userId);
        Assert.assertNotNull(userInfoList);
    }

    @Test
    public void queryExceptionInfo() {
        String infofeeId = "4127";
        List<ExceptionInfo> exceptionInfo = infofeeDetailMapper.queryExceptionInfo(infofeeId, ExPartyEnum.车主上报.getDicKey(),ExPartyEnum.车主上报.getNewDicKey(),"1");
        System.out.println(JSON.toJSONString(exceptionInfo));
        Assert.assertNotNull(exceptionInfo);
    }

    @Test
    public void testEqualNull() {
        boolean isRefund = InfofeeStatusEnum.已退款.getId().equals(null);

        Assert.assertTrue(isRefund);

    }
}