package com.tyt.mybatis.mapper;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations={
        "classpath:/config/spring/spring-common.xml",
        "classpath:/config/spring/spring-tytrecommend.xml"})
public class DemoMapperTest {

    @Autowired
    private DemoMapper demoMapper;

    @Test
    public void countMt() {
        int countMt = demoMapper.countMt();

        Assert.assertEquals(1692, countMt);
    }
}