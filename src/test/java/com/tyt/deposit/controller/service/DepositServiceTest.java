package com.tyt.deposit.controller.service;

import com.alibaba.fastjson.JSONObject;
import com.tyt.deposit.pojo.vo.AppletEntranceInfo;
import com.tyt.deposit.service.DepositService;
import com.tyt.plat.test.base.TytTestBase;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @since 2023/12/18 14:31
 */
public class DepositServiceTest extends TytTestBase {
    @Autowired
    private DepositService depositService;

    @Test
    public void getAppletEntranceInfoTest(){
        AppletEntranceInfo appletEntranceInfo = depositService.getAppletEntranceInfo(147373L);
        System.out.println(JSONObject.toJSONString(appletEntranceInfo));
    }
}
