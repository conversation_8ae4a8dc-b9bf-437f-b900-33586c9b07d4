package com.tyt.deposit.controller;

import com.tyt.excellentgoodshomepage.bean.TytExcellentGoodsCardUserDetail;
import com.tyt.excellentgoodshomepage.bean.TytExcellentGoodsCardUserDetailCanUseCountVO;
import com.tyt.excellentgoodshomepage.service.ExcellentGoodsService;
import com.tyt.plat.test.base.TytTestBase;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023/09/13 16:31
 */
@Slf4j
public class DepositControllerTest extends TytTestBase {

    @Autowired
    private ExcellentGoodsService excellentGoodsService;

    @Test
    public void testQueryEquity() throws Exception {
        mockMvc.perform(MockMvcRequestBuilders.get("/plat/deposit/queryEquity")
                        .param("userId", "1000000591"))
                .andDo((it) -> log.info(new String(it.getResponse().getContentAsByteArray())));
    }

    @Test
    public void test2() {
        List<TytExcellentGoodsCardUserDetail> allCanUseCarListByUserId = excellentGoodsService.getAllCanUseCarListByUserIdPage(10001L, 1);
        System.out.println(1);
    }

    @Test
    public void test3() {
        TytExcellentGoodsCardUserDetailCanUseCountVO allCanUseCarCountNumByUserId = excellentGoodsService.getAllCanUseCarCountNumMax100AndLimitTimeByUserId(10001L);
        System.out.println(1);
    }

    @Test
    public void test4() {
        try {
            Long id = excellentGoodsService.saveExcellentGoodsPriceCard(1002000942L,false);
            System.out.println(id);
        }catch(Exception e){
            System.out.println(2);
        }
    }


}