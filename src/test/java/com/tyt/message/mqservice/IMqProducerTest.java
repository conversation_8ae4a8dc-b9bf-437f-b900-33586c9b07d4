package com.tyt.message.mqservice;

import com.tyt.infofee.bean.MqBaseMessageBean;
import com.tyt.infofee.bean.MqUserMsg;
import com.tyt.plat.test.base.TytTestBase;
import com.tyt.util.SerialNumUtil;
import org.junit.Test;

import javax.annotation.Resource;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2022/4/8 10:46
 */
public class IMqProducerTest extends TytTestBase {

    @Resource
    private IMqProducer producer;

    @Test
    public void sendMsgTest() {

        MqUserMsg mqUserMsg = new MqUserMsg();


    }
}