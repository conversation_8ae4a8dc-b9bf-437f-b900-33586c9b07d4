package com.tyt.infofee.controller;

import com.alibaba.fastjson.JSON;
import com.tyt.goods.service.UserBuyGoodsService;
import com.tyt.plat.test.base.TytTestBase;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockHttpServletRequest;

@Slf4j
public class InfofeeDetailControllerTest extends TytTestBase {

    @Autowired
    private InfofeeDetailController infofeeDetailController;
    @Autowired
    private UserBuyGoodsService userBuyGoodsService;

    @Test
    public void testQueryInfofeeDetail() {
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.setParameter("clientSign", "32");
        log.info("{}", JSON.toJSONString(infofeeDetailController.queryInfofeeDetail("23110900000038", "1002000274", 2, null, request)))
        ;
    }
    @Test
    public void testQueryInfofeeDetail11() {
        Integer buyGoodsNum = userBuyGoodsService.getUserBuyGoodsRecord(1001000248L,1);
        System.out.println(buyGoodsNum);
    }
}