package com.tyt.infofee.controller;

import com.alibaba.fastjson.JSON;
import com.tyt.base.bean.BaseParameter;
import com.tyt.model.ResultMsgBean;
import com.tyt.plat.test.base.TytTestBase;
import com.tyt.plat.vo.ts.TransportUpdateDataReq;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2023/3/27 16:50
 */
public class TransportGoodsControllerTest extends TytTestBase {

    private static final Logger log = LoggerFactory.getLogger(TransportGoodsControllerTest.class);
    @Autowired
    TransportGoodsController transportGoodsController;

    TransportUpdateDataReq updateDataReq;

    {
        updateDataReq = new TransportUpdateDataReq();
        updateDataReq.setTsId(88821153L);
        updateDataReq.setUserId(1002001059L);
        updateDataReq.setClientVersion("6500");
        updateDataReq.setClientSign("21");
    }

    @Test
    public void testGetGoodsDynamic() {

        ResultMsgBean goodsDynamic = transportGoodsController.getGoodsDynamic(new BaseParameter(), null);
        log.info("goodsDynamic:{}", JSON.toJSONString(goodsDynamic));

    }

    @Test
    public void testUpdateGoodsInfo1() {
        // 1. 只更新长宽高
        updateDataReq.setHigh("1");
        ResultMsgBean resultMsgBean = transportGoodsController.updateGoodsInfo(updateDataReq);
        log.info("resultMsgBean:{}", JSON.toJSONString(resultMsgBean));
    }

    @Test
    public void testUpdateGoodsInfo2() {
        // 1. 只更新运费
        updateDataReq.setPrice("2000");
        ResultMsgBean resultMsgBean = transportGoodsController.updateGoodsInfo(updateDataReq);
        log.info("resultMsgBean:{}", JSON.toJSONString(resultMsgBean));
    }

    @Test
    public void testUpdateGoodsInfo3() {
        // 1. 只更新转一口价
        updateDataReq.setPublishType(2);
        ResultMsgBean resultMsgBean = transportGoodsController.updateGoodsInfo(updateDataReq);
        log.info("resultMsgBean:{}", JSON.toJSONString(resultMsgBean));
    }

    @Test
    public void testUpdateGoodsInfo4() {
        // 1. 只更新目的地
        updateDataReq.setDestLatitude(new BigDecimal("30.918112"));
        updateDataReq.setDestLongitude(new BigDecimal("113.963841"));
        // updateDataReq.setDestCoordX(new BigDecimal("204.65"));
        // updateDataReq.setDestCoordY(new BigDecimal("3425.45"));
        updateDataReq.setDestPoint("湖北孝感市孝南区");
        updateDataReq.setDestProvinc("湖北");
        updateDataReq.setDestCity("孝感市");
        updateDataReq.setDestArea("孝南区");
        updateDataReq.setDestDetailAdd("吾悦广场旁 孝感人家");
        updateDataReq.setDistance("1123");
        ResultMsgBean resultMsgBean = transportGoodsController.updateGoodsInfo(updateDataReq);
        log.info("resultMsgBean:{}", JSON.toJSONString(resultMsgBean));
    }

    @Test
    public void testUpdateGoodsInfo5() {
        // 1. 更新运费+一口价
        updateDataReq.setPrice("2000");
        updateDataReq.setPublishType(2);
        ResultMsgBean resultMsgBean = transportGoodsController.updateGoodsInfo(updateDataReq);
        log.info("resultMsgBean:{}", JSON.toJSONString(resultMsgBean));
    }

    @Test
    public void testUpdateGoodsInfo6() {
        // 1. 更新运费+一口价+目的地
        updateDataReq.setPrice("3000");
        updateDataReq.setPublishType(2);
        updateDataReq.setDestLatitude(new BigDecimal("30.918112"));
        updateDataReq.setDestLongitude(new BigDecimal("113.963841"));
        // updateDataReq.setDestCoordX(new BigDecimal("204.65"));
        // updateDataReq.setDestCoordY(new BigDecimal("3425.45"));
        updateDataReq.setDestPoint("湖北孝感市孝南区");
        updateDataReq.setDestProvinc("湖北");
        updateDataReq.setDestCity("孝感市");
        updateDataReq.setDestArea("孝南区");
        updateDataReq.setDestDetailAdd("吾悦广场旁 孝感人家");
        updateDataReq.setDistance("1123");
        ResultMsgBean resultMsgBean = transportGoodsController.updateGoodsInfo(updateDataReq);
        log.info("resultMsgBean:{}", JSON.toJSONString(resultMsgBean));
    }
}