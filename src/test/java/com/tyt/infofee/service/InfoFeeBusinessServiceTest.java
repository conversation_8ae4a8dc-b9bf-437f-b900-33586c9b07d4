package com.tyt.infofee.service;

import com.tyt.base.bean.BaseParameter;
import com.tyt.infofee.dao.TransportOrdersDao;
import com.tyt.model.TransportMain;
import com.tyt.model.TytTransportOrders;
import com.tyt.plat.entity.base.TytTransportOrderSnapshot;
import com.tyt.plat.test.base.TytTestBase;
import com.tyt.transport.dao.TransportMainDao;
import com.tyt.transport.service.BsPublishTransportService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2022/3/17 13:54
 */
public class InfoFeeBusinessServiceTest extends TytTestBase {

    @Autowired
    private InfoFeeBusinessService infoFeeBusinessService;

    @Autowired
    private TransportMainDao transportMainDao;

    @Autowired
    private TransportOrdersDao transportOrdersDao;

    @Autowired
    private BsPublishTransportService bsPublishTransportService;

    @Test
    public void saveTransportOrderSnapshot() {

        TransportMain transportMain = transportMainDao.findById(33827715L);

        TytTransportOrders orders = transportOrdersDao.findById(8397L);

        TytTransportOrderSnapshot tytTransportOrderSnapshot = infoFeeBusinessService.saveTransportOrderSnapshot(transportMain, orders, null,null,null);

        System.out.println("finished ... ");

    }

    public void testGetGoodsInfoDetail() {


    }

    @Test
    public void testr23r() {
        BaseParameter baseParameter = new BaseParameter();
        baseParameter.setClientVersion("6550");
        baseParameter.setClientSign("32");
        bsPublishTransportService.transportAgreeQuotedPriceEditTransportPriceAndReComputTecServiceFeeAndInvoice(baseParameter, 88820254L, 800, 9999L, 1);
        System.out.println(1);
    }

}