package com.tyt.infofee.service;

import com.tyt.plat.test.base.TytTestBase;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024/06/03 14:15
 */
public class FeeCalculateServiceTest extends TytTestBase {

    @Autowired
    private FeeCalculateService feeCalculateService;


    @Test
    public void testCalculateTechServiceFee() {
        BigDecimal carriageFee = new BigDecimal(222.22);
        Integer excellentGoods = 2;
        BigDecimal fee = feeCalculateService.calculateTechServiceFee(carriageFee, excellentGoods);
        System.out.println(fee);
    }
}
