package com.tyt.infofee.service;

import com.tyt.model.ResultMsgBean;
import com.tyt.model.TransportMain;
import com.tyt.plat.test.base.TytTestBase;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.transportquotedprice.service.TransportQuotedPriceService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.Duration;
import java.time.temporal.ChronoUnit;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/10/19 14:29
 */
public class TransportOrdersServiceTest extends TytTestBase {

    @Autowired
    private TransportOrdersService transportOrdersService;

    @Autowired
    private TransportQuotedPriceService transportQuotedPriceService;

    private static final String TRANSPORT_QUOTED_PRICE_COUNT_HASH_KEY = "transportQuotedPriceCount";

    private static final String TRANSPORT_QUOTED_PRICE_DETAINMENT_HASH_KEY = "transportQuotedPriceCountDetainment";

    private static final String TRANSPORT_CALL_PHONE_RECORED_COUNT_HASH_KEY = "transportCallPhoneRecoredCount";


    @Test
    public void testCheckDepositReadOnly() {
        TransportMain transportMain = new TransportMain();
        transportMain.setSrcMsgId(88888L);
        transportMain.setUserId(10001L);
        transportQuotedPriceService.recordCarQuotedPrice(transportMain);
        System.out.println(1);

    }

    @Test
    public void test4324() {
        Long userId = 10001L;
        Long srcMsgId = 88888L;
        if (RedisUtil.exists(TRANSPORT_QUOTED_PRICE_COUNT_HASH_KEY + ":" + userId)
                && RedisUtil.mapExists(TRANSPORT_QUOTED_PRICE_COUNT_HASH_KEY + ":" + userId, srcMsgId.toString())) {
            //将该货源的货主未浏览报价次数清空
            RedisUtil.mapRemove(TRANSPORT_QUOTED_PRICE_COUNT_HASH_KEY + ":" + userId, srcMsgId.toString());
        }
    }

    @Test
    public void test43242() {
        //是优车定价货源
        Long userId = 10001L;
        Long srcMsgId = 88888L;
        String transportViewCount = RedisUtil.get(TRANSPORT_QUOTED_PRICE_DETAINMENT_HASH_KEY + ":" + userId + "-" + srcMsgId.toString());
        if (transportViewCount != null) {
            if (Integer.valueOf(transportViewCount) == 1) {
                //车方第二次浏览优车2.0货源，货源详情增加挽留弹窗字段
                System.out.println(111);
//                result.getDetailBean().setGoodCarPriceTransportDetainment(1);
            }
            RedisUtil.set(TRANSPORT_QUOTED_PRICE_DETAINMENT_HASH_KEY + ":" + userId + "-" + srcMsgId.toString(), (Integer.valueOf(transportViewCount) + 1) + "", 60 * 60 * 24);
        } else {
            RedisUtil.set(TRANSPORT_QUOTED_PRICE_DETAINMENT_HASH_KEY + ":" + userId + "-" + srcMsgId.toString(), "1", 60 * 60 * 24);
        }
    }

    @Test
    public void test1223 () {
        TransportMain transportMain = new TransportMain();
        transportMain.setSrcMsgId(86748427L);
        transportMain.setUserId(1000000485L);
        recordCarCallPhone(transportMain, 91919191L);

        transportMain.setSrcMsgId(86748427L);
        transportMain.setUserId(1000000485L);
        recordCarCallPhone(transportMain, 104341312L);

        transportMain.setSrcMsgId(86748426L);
        transportMain.setUserId(1000000485L);
        recordCarCallPhone(transportMain, 104341312L);
    }

    private void recordCarCallPhone(TransportMain transportMain, Long carUserId) {
        if (RedisUtil.exists(TRANSPORT_CALL_PHONE_RECORED_COUNT_HASH_KEY + ":" + transportMain.getUserId())) {
            if (!RedisUtil.mapExists(TRANSPORT_CALL_PHONE_RECORED_COUNT_HASH_KEY + ":" + transportMain.getUserId(), transportMain.getSrcMsgId().toString())) {
                RedisUtil.mapPut(TRANSPORT_CALL_PHONE_RECORED_COUNT_HASH_KEY + ":" + transportMain.getUserId(), transportMain.getSrcMsgId().toString(), carUserId.toString());
            } else {
                String mapValue = RedisUtil.getMapValue(TRANSPORT_CALL_PHONE_RECORED_COUNT_HASH_KEY + ":" + transportMain.getUserId(), transportMain.getSrcMsgId().toString());
                mapValue += ("," + carUserId.toString());
                RedisUtil.mapPut(TRANSPORT_CALL_PHONE_RECORED_COUNT_HASH_KEY + ":" + transportMain.getUserId(), transportMain.getSrcMsgId().toString(), mapValue);
            }
        } else {
            long secondsUntilTomorrow = Duration.between(java.time.LocalDateTime.now(), java.time.LocalDateTime.now().truncatedTo(ChronoUnit.DAYS).plusDays(1)).get(ChronoUnit.SECONDS);
            RedisUtil.setMap(TRANSPORT_CALL_PHONE_RECORED_COUNT_HASH_KEY + ":" + transportMain.getUserId(), transportMain.getSrcMsgId().toString(), carUserId.toString(), Integer.parseInt(String.valueOf(secondsUntilTomorrow)));
        }
    }

    @Test
    public void test4324324() {
        ResultMsgBean carHaveNewTransportQuotedPriceOrAgreeQuotedPrice = transportQuotedPriceService.getCarHaveNewTransportQuotedPriceOrAgreeQuotedPrice(1000003064L);
        System.out.println(1);
    }

}