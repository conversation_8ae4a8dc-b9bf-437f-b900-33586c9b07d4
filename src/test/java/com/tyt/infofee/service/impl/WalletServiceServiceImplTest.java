package com.tyt.infofee.service.impl;

import com.tyt.infofee.service.WalletService;
import com.tyt.plat.test.base.TytTestBase;
import junit.framework.TestCase;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2024/1/20 14:07
 */
public class WalletServiceServiceImplTest extends TytTestBase {

    @Autowired
    private WalletService walletService;

    @Test
    public void testGetManBangUserAccountBalance() throws Exception{

        Long userId = 1002000418L;

        BigDecimal manBangUserAccountBalance = walletService.getManBangUserAccountBalance(userId + "", "");

        System.out.println("" + manBangUserAccountBalance);

        assertTrue(true);

    }
}