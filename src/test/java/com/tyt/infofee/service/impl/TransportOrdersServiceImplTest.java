package com.tyt.infofee.service.impl;

import com.tyt.infofee.enums.InfofeeStatusEnum;
import com.tyt.infofee.service.TransportOrdersService;
import com.tyt.model.TytTransportOrders;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;

import static org.junit.Assert.*;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations={
        "classpath:/config/spring/spring-common.xml",
        "classpath:/config/spring/spring-tytrecommend.xml"})
public class TransportOrdersServiceImplTest {
    @Autowired
    private TransportOrdersService transportOrdersService;

    //-- tsOrderNo:18102100000025, payUserId: 148368
    @Test
    public void testOfflineOrder() {
        TytTransportOrders transportOrders = transportOrdersService.getTytOfflineTransportOrders("18102100000025", 148368l);

        assertNotNull(transportOrders);
    }

    @Test
    public void testRobStatus(){
        long id = 3391l;
        String robStatus = "4";
        try {
            transportOrdersService.saveChangeRobStatusById(id ,robStatus);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void getNewTransportOrders() {
        TytTransportOrders transportOrders = transportOrdersService.getNewTransportOrders("19011600000013", 148193l, InfofeeStatusEnum.已支付);
        assertNotNull(transportOrders);
    }

    @Test
    public void getOtherPaidOrders() {
        long userId = 148075;
        Long[] noPaidUserId = {147986l};
        String tsOrderNo = "19011700000081";
        List<TytTransportOrders> transportOrdersList = transportOrdersService.getOtherPaidOrders(userId, tsOrderNo, noPaidUserId);
        assertEquals(transportOrdersList.size(), 1);
    }
}