package com.tyt.infofee.service.impl;

import com.tyt.AbstractTestCase;
import com.tyt.infofee.bean.InfoFeeDetail;
import com.tyt.infofee.service.IInfofeeDetailService;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class InfofeeDetailServiceImplTest extends AbstractTestCase {

    @Autowired
    private IInfofeeDetailService infofeeDetailService;

    @Test
    public void ownerInfofeeDetail() {
        String tsOrderNo = "19021800000046";
        String ownerId = "147943";
        InfoFeeDetail infoFeeDetail = infofeeDetailService.ownerInfofeeDetail(tsOrderNo, ownerId, true, null);
        Assert.assertNotNull(infoFeeDetail);
        Assert.assertEquals("1", infoFeeDetail.getBaseTransInfo().getUserType());
    }

    @Test
    public void driverInfofeeDetail() {
        String tsOrderNo = "19021600000027";
        String userId = "147943";
        InfoFeeDetail infoFeeDetail = infofeeDetailService.driverInfofeeDetail(tsOrderNo, userId, true, null, null);
        Assert.assertNotNull(infoFeeDetail);
        Assert.assertEquals("1", infoFeeDetail.getInfoFeePayinfoList().get(0).getDriverUserInfo().getUserType());
    }

    @Test
    public void addInfofeeStatusLog() {
    }

    @Test
    public void syncWalletInfo() {
    }
}