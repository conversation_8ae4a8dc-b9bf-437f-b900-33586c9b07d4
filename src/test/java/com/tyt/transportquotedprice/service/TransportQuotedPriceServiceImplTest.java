package com.tyt.transportquotedprice.service;

import com.tyt.base.bean.BaseParameter;
import com.tyt.plat.test.base.TytTestBase;
import org.junit.Test;

import javax.annotation.Resource;

import static org.junit.Assert.*;

/**
 *
 *
 * <AUTHOR>
 * @since 2025-06-04 11:10
 */
public class TransportQuotedPriceServiceImplTest extends TytTestBase  {

    @Resource
    private TransportQuotedPriceService transportQuotedPriceService;

    @Test
    public void carQuotedPrice() {
        BaseParameter baseParameter = new BaseParameter();
        baseParameter.setClientSign("");
        baseParameter.setClientVersion("6690");
        transportQuotedPriceService.carQuotedPrice(1002001075L, 88824315L, 5700, "test", baseParameter);
    }
}