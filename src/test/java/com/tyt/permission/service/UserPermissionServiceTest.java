package com.tyt.permission.service;

import com.tyt.goods.service.UserBuyGoodsService;
import com.tyt.model.User;
import com.tyt.model.UserPermissionResult;
import com.tyt.permission.bean.Permission;
import com.tyt.permission.bean.PermissionResult;
import com.tyt.plat.test.base.TytTestBase;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.user.service.UserService;
import com.tyt.util.Constant;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2022/4/29 11:30
 */
public class UserPermissionServiceTest extends TytTestBase {

    @Autowired
    private UserPermissionService userPermissionService;
    @Autowired
    private UserService userService;
    @Autowired
    private UserBuyGoodsService userBuyGoodsService;

    @Test
    public void updateAuthPermissionReturn() {

        long userId = 589359L;

                   Integer buyVipGoodsNum = userBuyGoodsService.getUserBuyGoodsRecord(userId,1);
        // 判断是否是会员，如果是会员，判断购买金额是否大于1
        boolean vipPublishPermission = userPermissionService.isVipPublishPermission(userId);
        // 判断是否是次卡
        Integer buyCardGoodsNum = userBuyGoodsService.getUserBuyGoodsRecord(userId,2);
        boolean publishCardPermission = userPermissionService.isPublishCardPermission(userId);
        // 是会员是次卡
        if (vipPublishPermission && publishCardPermission) {
            if ((buyVipGoodsNum == null || buyVipGoodsNum <= 0) && (buyCardGoodsNum == null || buyCardGoodsNum <= 0)) {
                System.out.println(true);
            }
        } else if (vipPublishPermission && !publishCardPermission) {
            if (buyVipGoodsNum == null || buyVipGoodsNum <= 0) {
                System.out.println(true);
            }
        } else if (!vipPublishPermission && publishCardPermission) {
            if ((buyCardGoodsNum == null || buyCardGoodsNum <= 0)) {
                System.out.println(true);
            }
        } else {
            System.out.println(true);
        }

        //登录更新用户权益缓存
        UserPermissionResult result = new UserPermissionResult();
        PermissionResult permissionResult = userPermissionService.updateAuthPermissionReturn(Permission.用户昵称显示, userId);
        if (permissionResult.getUse()){
            result.setIsUserNamePower(1);
        }
        PermissionResult contentPermissionResult = userPermissionService.updateAuthPermissionReturn(Permission.货物内容显示, userId);
        if (contentPermissionResult.getUse()){
            result.setIsContentPower(1);
        }

        System.out.println("finished ... ");
    }

    @Test
    public void giveExperienceMember() {
        try {
            User user = userService.getById(1002000816L);
            userPermissionService.giveExperienceMember(user,2);
        }catch (Exception e){
            e.printStackTrace();
        }
        System.out.println("finished ... ");
    }
}