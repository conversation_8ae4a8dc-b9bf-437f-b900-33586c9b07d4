package com.tyt.invoice;

import com.tyt.invoicetransport.service.InvoiceTransportService;
import com.tyt.model.ResultMsgBean;
import com.tyt.plat.biz.invoice.db.InvoiceDbService;
import com.tyt.plat.biz.invoice.serivce.InvoiceCarService;
import com.tyt.plat.entity.base.TytCarVO;
import com.tyt.plat.entity.base.TytInvoiceDriver;
import com.tyt.plat.test.base.TytTestBase;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;

public class TestCarAndDriverTest extends TytTestBase {

    @Autowired
    private InvoiceCarService invoiceCarService;

    @Autowired
    private InvoiceDbService invoiceDbService;

    @Autowired
    private InvoiceTransportService invoiceTransportService;

    @Test
    public void getMasterDriver(){
        TytCarVO masterDriver = invoiceCarService.getMasterDriver(2l);
        System.out.println(masterDriver);
    }

    @Test
    public void getDriverList(){
        List<TytInvoiceDriver> tytInvoiceDrivers = invoiceDbService.listByUserIdAndVerifyStatus(1000000952l, null);
        System.out.println(tytInvoiceDrivers);
    }

    @Test
    public void testBase(){
        ResultMsgBean additionalPriceAndEnterpriseTaxRate = invoiceTransportService.getAdditionalPriceAndEnterpriseTaxRate(10001L, "10", 0L);
        if (additionalPriceAndEnterpriseTaxRate.getCode() == 200) {
            HashMap<String, String> additionalPriceAndEnterpriseTaxRateResult = (HashMap<String, String>) additionalPriceAndEnterpriseTaxRate.getData();
            System.out.println(additionalPriceAndEnterpriseTaxRateResult);
        }
        System.out.println(1);
    }
}
