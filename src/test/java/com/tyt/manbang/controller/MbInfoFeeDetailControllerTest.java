package com.tyt.manbang.controller;

import com.gexin.fastjson.JSONObject;
import com.tyt.infofee.service.TransportOrdersService;
import com.tyt.manbang.bean.request.MbOrderCreateSucNoticeBean;
import com.tyt.manbang.bean.request.MbOrderRefundNoticeBean;
import com.tyt.manbang.service.MbInfoFeeDetailService;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.Transport;
import com.tyt.model.TytTransportOrders;
import com.tyt.plat.test.base.TytTestBase;
import com.tyt.transport.querybean.TransportDoneRequest;
import com.tyt.transport.service.TransportBusinessInterface;
import com.tyt.util.ReturnCodeConstant;
import org.junit.Test;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;


public class MbInfoFeeDetailControllerTest extends TytTestBase {

    @Resource(name = "mbInfoFeeDetailService")
    private MbInfoFeeDetailService mbInfoFeeDetailService;

    @Resource(name = "transportOrdersService")
    private TransportOrdersService transportOrdersService;

    @Resource(name = "transportBusiness")
    TransportBusinessInterface transportBusiness;

    @Test
    public void testCheckIsSeckillGoods() throws Exception {
        Transport transport = transportBusiness.getByGoodsId(88822576L);
        boolean b = transportBusiness.checkIsSeckillGoods(transport, BigDecimal.TEN);
        System.out.println(b);
    }

    @Test
    public void saveOrderCreateSuccessInfo() {
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "成功");
        MbOrderCreateSucNoticeBean orderCreateSucNoticeBean= new MbOrderCreateSucNoticeBean();
        orderCreateSucNoticeBean.setCargoId(78828506l);
        orderCreateSucNoticeBean.setOrderId(100l);
        orderCreateSucNoticeBean.setPayDepositTime(new Date().getTime());
        orderCreateSucNoticeBean.setDeposit(100l);
        orderCreateSucNoticeBean.setOrderCreatedTime(new Date().getTime());
        orderCreateSucNoticeBean.setDriverName("苑小美");
        orderCreateSucNoticeBean.setDriverTelephone(Long.valueOf("***********"));
        orderCreateSucNoticeBean.setDriverCarNo("河1234566");
        System.out.println(JSONObject.toJSONString(orderCreateSucNoticeBean));
        try {
            mbInfoFeeDetailService.saveMbPayOrderBusiness(orderCreateSucNoticeBean,orderCreateSucNoticeBean.getCargoId(),resultMsgBean);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Test
    public void saveInfoFeeUpdateBtnStatusNew() throws Exception{
        Long syncUserId=1002000055l;
        TransportDoneRequest doneRequest= new TransportDoneRequest();
        doneRequest.setHeadCity("河");
        doneRequest.setHeadNo("123456");
        transportBusiness.saveInfoFeeUpdateBtnStatusNew(syncUserId, 1, 33831123l, doneRequest, null, 3 ,null,null,true, null, null);

    }

    @Test
    public void handlerOrderRefundInfo() throws Exception {
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "成功");
        MbOrderRefundNoticeBean mbOrderRefundNoticeBean= new MbOrderRefundNoticeBean();
        mbOrderRefundNoticeBean.setCargoId(78828497l);
        mbOrderRefundNoticeBean.setOrderId(100l);
        mbOrderRefundNoticeBean.setAmount(1000l);
        mbOrderRefundNoticeBean.setDeductSuccessTime(new Date().getTime());
        mbOrderRefundNoticeBean.setReasonDesc("测试你数据喽");
        mbOrderRefundNoticeBean.setStatus("refundedToDriver");
        String thirdPartyPlatformOrderNo="100";
        TytTransportOrders tranSportOrders = transportOrdersService.getByThirdPartyPlatformOrderNo(thirdPartyPlatformOrderNo);
        mbInfoFeeDetailService.handlerRefundToDriverBusiness(mbOrderRefundNoticeBean,tranSportOrders,resultMsgBean);
    }


    @Test
    public void handlerSettledToShipperBusiness() throws Exception {
        ResultMsgBean resultMsgBean = new ResultMsgBean(ReturnCodeConstant.OK, "成功");
        MbOrderRefundNoticeBean mbOrderRefundNoticeBean= new MbOrderRefundNoticeBean();
        mbOrderRefundNoticeBean.setCargoId(78828497l);
        mbOrderRefundNoticeBean.setOrderId(100l);
        mbOrderRefundNoticeBean.setAmount(1000l);
        mbOrderRefundNoticeBean.setDeductSuccessTime(new Date().getTime());
        mbOrderRefundNoticeBean.setReasonDesc("测试你数据喽");
        mbOrderRefundNoticeBean.setStatus("refundedToDriver");
        String thirdPartyPlatformOrderNo="100";
        TytTransportOrders tranSportOrders = transportOrdersService.getByThirdPartyPlatformOrderNo(thirdPartyPlatformOrderNo);
        mbInfoFeeDetailService.handlerSettledToShipperBusiness(mbOrderRefundNoticeBean,tranSportOrders,resultMsgBean);
    }
}