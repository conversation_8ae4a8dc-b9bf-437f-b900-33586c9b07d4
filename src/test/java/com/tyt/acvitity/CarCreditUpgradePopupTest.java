package com.tyt.acvitity;

import com.alibaba.fastjson.JSON;
import com.tyt.acvitity.service.ActivityGradePrizeService;
import com.tyt.acvitity.service.TytCarCreditUpgradePopupService;
import com.tyt.marketingActivity.bean.MarketingActivityPopupBean;
import com.tyt.plat.test.base.TytTestBase;
import com.tyt.util.AddressCuttingUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class CarCreditUpgradePopupTest extends TytTestBase {

    @Autowired
    private TytCarCreditUpgradePopupService carCreditUpgradePopupService;


    @Autowired
    ActivityGradePrizeService activityGradePrizeService;

    @Test
    public void getCreditUpgradePopupInfoTes(){
        /*MarketingActivityPopupBean creditUpgradePopupInfo = carCreditUpgradePopupService.getCreditUpgradePopupInfo(1000000573L);
        System.out.println("信用弹窗:"+JSON.toJSON(creditUpgradePopupInfo));*/

        //String address = AddressCuttingUtil.addressCutting("广东省广州市南沙区入口,东北方向,64.0米");
        activityGradePrizeService.sendMq(1002000771L, "15835264214", 23);
    }
}
