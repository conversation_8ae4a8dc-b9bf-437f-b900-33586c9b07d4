package com.tyt.acvitity.service;

import com.tyt.common.bean.PageData;
import com.tyt.plat.entity.base.ExposurePermissionGainRecord;
import com.tyt.plat.test.base.TytTestBase;
import junit.framework.TestCase;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2024/7/2 15:01
 */
public class ExposurePermissionServiceTest extends TytTestBase {

    @Autowired
    private ExposurePermissionService exposurePermissionService;

    @Test
    public void testGetExposurePermissionGainRecordListByUserId() {

        Long userId = 1000002907L;
        PageData<ExposurePermissionGainRecord> exposurePermissionGainRecordListByUserId = exposurePermissionService.getExposurePermissionGainRecordListByUserId(userId, 1, 20);

        System.out.println("finished ... ");

        assert true;
    }
}