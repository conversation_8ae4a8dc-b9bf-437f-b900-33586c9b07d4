package com.tyt.acvitity;

import com.tyt.acvitity.bean.UserCenterExposureInfo;
import com.tyt.acvitity.dao.DrawActivityInfoDao;
import com.tyt.acvitity.helper.LotteryCheckService;
import com.tyt.acvitity.service.ExposurePermissionService;
import com.tyt.acvitity.service.LotteryRecordService;
import com.tyt.model.DrawActivityInfo;
import com.tyt.model.ResultMsgBean;
import com.tyt.plat.entity.base.DrawActivityInfoEntity;
import com.tyt.plat.mapper.base.DrawActivityInfoMapper;
import com.tyt.plat.mapper.base.ExposurePermissionGainRecordMapper;
import com.tyt.plat.test.base.TytTestBase;
import com.tyt.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/03/22
 */
@Slf4j
public class LotteryRecordTest extends TytTestBase {

    @Autowired
    private LotteryRecordService lotteryRecordService;

    @Autowired
    private LotteryCheckService lotteryCheckService;
    @Autowired
    private DrawActivityInfoDao drawActivityInfoDao;

    @Autowired
    private ExposurePermissionGainRecordMapper exposurePermissionGainRecordMapper;

    @Autowired
    private ExposurePermissionService exposurePermissionService;

    @Test
    public void testActivityLottery() {
        for (int i = 0; i < 10; i++) {
            log.info("{}", lotteryRecordService.activityLottery(1002000788L, 105L, 0L));
        }
    }

    @Test
    public void lotteryCheckServiceTest(){
        DrawActivityInfo drawActivityInfo = drawActivityInfoDao.findById(105L);
        lotteryCheckService.setOrderStimulateExecuteType(1);
        ResultMsgBean resultMsgBean = lotteryCheckService.checkLottery(1002000788L,drawActivityInfo,null);
        log.info(resultMsgBean.toString());
    }

    @Test
    public void exposurePermissionGainRecordTest(){
//        String minusDays = TimeUtil.minusDays(7);
//        List<UserCenterExposureInfo> userCenterExposureInfos = exposurePermissionGainRecordMapper.selectUserCenterExposureInfo(1000000573L, minusDays);
//        System.out.println(userCenterExposureInfos);

        List<UserCenterExposureInfo> userCenterExposureInfo = exposurePermissionService.getUserCenterExposureInfo(1000000111L);
        System.out.println(userCenterExposureInfo);
    }
}
