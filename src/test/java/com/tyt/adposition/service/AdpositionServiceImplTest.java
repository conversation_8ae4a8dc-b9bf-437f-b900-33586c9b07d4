package com.tyt.adposition.service;

import com.alibaba.fastjson.JSON;
import com.tyt.AbstractTestCase;
import com.tyt.acvitity.bean.ConventionOrdersCensusVo;
import com.tyt.acvitity.bean.ConventionRankListVo;
import com.tyt.acvitity.service.ConventionOrdersCensusService;
import com.tyt.acvitity.service.GuaranteeActivityService;
import com.tyt.adposition.service.impl.AdPositionServiceImpl;
import com.tyt.infofee.bean.InfoFeeDetail;
import com.tyt.infofee.service.IInfofeeDetailService;
import com.tyt.model.AdPosition;
import com.tyt.model.ConventionOrdersCensus;
import com.tyt.model.User;
import com.tyt.plat.utils.DateUtil;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.transport.service.PrivacyPhoneNumService;
import com.tyt.user.service.UserService;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

public class AdpositionServiceImplTest extends AbstractTestCase {

    @Autowired
    private AdPositionService adPositionService;

    @Autowired
    private GuaranteeActivityService guaranteeActivityService;

    @Autowired
    private ConventionOrdersCensusService conventionOrdersCensusService;

    @Autowired
    private UserService userService;

    @Autowired
    private PrivacyPhoneNumService privacyPhoneNumService;

    @Test
    public void test() {
//        ConventionRankListVo currentOrderNumAndRankList = conventionOrdersCensusService.getCurrentOrderNumAndRankList(64576L);
//        System.out.println(JSON.toJSONString(currentOrderNumAndRankList));
//        List<ConventionOrdersCensusVo> currentOrderNumAndRankList1 = conventionOrdersCensusService.getPrizeLog(64576L,1L);
//        System.out.println(JSON.toJSONString(currentOrderNumAndRankList1));
        Long[] adPositionIds= new Long[730];
        adPositionService.checkShow(adPositionIds,33870633l);
    }

    @Test
    public void test2() {
        int[] adPositionIds= {60};
        User user = null;
        try {
            user = userService.getByUserId(1002000942L);
            Map<String, Object> map = adPositionService.getAdListForApp(adPositionIds,true,"22",user,"北京");
            System.out.println(JSON.toJSONString(map));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    public void test111() {
        String s = privacyPhoneNumService.makeAXBUserFieldParam(1002000094L, 88820018L);
        System.out.println(s);
    }

}