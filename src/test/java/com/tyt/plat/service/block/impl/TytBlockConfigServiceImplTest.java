package com.tyt.plat.service.block.impl;

import com.alibaba.fastjson.JSONObject;
import com.tyt.plat.entity.base.TytSigningCarVO;
import com.tyt.plat.service.block.TytBlockConfigService;
import com.tyt.plat.test.base.TytTestBase;
import com.tyt.transport.bean.AssignableCarFilterBean;
import com.tyt.transport.enums.DriverDrivingEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @since 2024-06-05 16:26
 */
@Slf4j
public class TytBlockConfigServiceImplTest extends TytTestBase {

    @Autowired
    private TytBlockConfigService tytBlockConfigService;

    @Test
    public void getAssignableCars() {
        AssignableCarFilterBean filterBean = new AssignableCarFilterBean();
        filterBean.setSrcMsgId(88822321L);
        filterBean.setStartProvince("河北");
        filterBean.setStartCity("唐山市");
        filterBean.setStartArea("丰南区");
        filterBean.setDestProvince("河北");
        filterBean.setDestCity("保定市");
        // filterBean.setDriverDriving(DriverDrivingEnum.NOT_DRIVING.getCode());
        filterBean.setGoodsTypeName("装载机");
        // filterBean.setCargoOwnerId(26L);
        filterBean.setDistanceLimit(100);
        filterBean.setExcellentGoods(0);
        List<TytSigningCarVO> assignableCars = tytBlockConfigService.getAssignableCars(filterBean, null);
        log.info("result :{}", JSONObject.toJSONString(assignableCars));
    }

    @Test
    public void getCarUser() {
        List<TytSigningCarVO> carUser = tytBlockConfigService.getCarUser("11,21,22", "北京市", "西城区");
        log.info("result:{}", JSONObject.toJSONString(carUser));
    }
}