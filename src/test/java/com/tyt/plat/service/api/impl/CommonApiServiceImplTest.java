package com.tyt.plat.service.api.impl;

import com.tyt.acvitity.service.StimulateCarLocationService;
import com.tyt.infofee.service.TransportOrdersService;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytTransportOrders;
import com.tyt.plat.service.api.CommonApiService;
import com.tyt.plat.test.base.TytTestBase;
import com.tyt.plat.vo.axb.AxbBindReq;
import com.tyt.plat.vo.axb.AxbBindVO;
import com.tyt.plat.vo.axb.AxbUpdateReq;
import com.tyt.plat.vo.ocr.OcrBusinessLicenseVo;
import com.tyt.plat.vo.ocr.OcrIdCardBackVo;
import com.tyt.plat.vo.ocr.OcrIdCardFrontVo;
import com.tyt.user.service.BlacklistUserService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2023/3/20 18:34
 */
public class CommonApiServiceImplTest extends TytTestBase {

    @Autowired
    private CommonApiService commonApiService;

    @Autowired
    private StimulateCarLocationService stimulateCarLocationService;

    @Resource
    private TransportOrdersService transportOrdersService;

    @Autowired
    private BlacklistUserService blacklistUserService;

    @Test
    public void testSplitContent() {

        for (int i = 0; i < 100; i++) {

            try {
                String content = "测试小松测试挖测试200";
                List<String> strings = commonApiService.splitContent(content);

                System.out.println(strings);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        System.out.println("finished ... ");

    }

    @Test
    public void testDoOcrByLicenseUrl() {

        String url = "http://devimage.teyuntong.net/dispatch/APP/2023-3-27/d9ae64a9391572b6d4f182684b04e089.png";
        url = "https://ai.bdstatic.com/file/511E1E48E7354A6BB82BEAA27D3D55EB";url = "https://ai.bdstatic.com/file/243C3F4EE199405C8AFB12249D18522E";

        OcrBusinessLicenseVo response = commonApiService.doOcrByLicenseUrl(url);

        System.out.println(response);
    }

    @Test
    public void testIdCardFrontOcr() {
        String url = "";
        OcrIdCardFrontVo ocrIdCardFrontVo = commonApiService.idCardFrontOcr(url);
        System.out.println(ocrIdCardFrontVo);
    }

    @Test
    public void testIdCardBackOcr() {
        String url = "";
        OcrIdCardBackVo idCardBackVo = commonApiService.idCardBackOcr(url);
        System.out.println(idCardBackVo);
    }

    @Test
    public void testAxbBind() {
        AxbBindReq req = new AxbBindReq();
        req.setTelA("***********");
        req.setTelB("***********");
        req.setBizId(13213L);
        req.setBizType(1);
        req.setExpiration(80);
        AxbBindVO result = commonApiService.axbBind(req);
        System.out.println(result);
    }

    @Test
    public void testAxbUpdate() {
        AxbUpdateReq req = new AxbUpdateReq();
        req.setId(19L);
        req.setExpiration(80);
        Object result = commonApiService.axbUpdate(req);
        System.out.println(result);
    }

    /**
     * 风险单轨迹判定单元测试
     */
    @Test
    public void testLocationDetermine() {

        TytTransportOrders transportOrders = transportOrdersService.getByTsId(33833506L);

        int i = stimulateCarLocationService.locationDetermine(transportOrders);

        System.out.println("判定结果："+i);
    }

    @Test
    public void checkLimitPublishTest(){
        ResultMsgBean resultMsgBean = blacklistUserService.checkLimitPublish(1000000772L);
        System.out.println(resultMsgBean);
    }
}