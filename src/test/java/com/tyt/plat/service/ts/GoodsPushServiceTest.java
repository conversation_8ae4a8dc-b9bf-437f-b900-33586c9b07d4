package com.tyt.plat.service.ts;

import com.tyt.infofee.service.TransportOrdersService;
import com.tyt.plat.test.base.TytTestBase;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/12/10 16:08
 */
public class GoodsPushServiceTest extends TytTestBase {

    @Autowired
    GoodsPushService goodsPushService;

    @Autowired
    TransportOrdersService transportOrdersService;

    @Test
    public void testPushGoodsTips() {
        Long userId = 1002000279L;
        Long srcMsgId = 33831732L;
        goodsPushService.pushGoodsTips(userId, srcMsgId);

    }

    @Test
    public void test() {
        Long userId = 1002000418L;
        int userType = 1;
        long userFeedbackTodoCount = transportOrdersService.getUserFeedbackTodoCount(userId, userType);
        System.out.println(userFeedbackTodoCount);
    }
}