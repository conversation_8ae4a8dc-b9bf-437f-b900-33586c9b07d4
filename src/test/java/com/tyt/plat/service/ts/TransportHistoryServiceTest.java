package com.tyt.plat.service.ts;

import com.tyt.model.Transport;
import com.tyt.plat.entity.base.TytTransportHistory;
import com.tyt.plat.test.base.TytTestBase;
import com.tyt.transport.service.TransportService;
import com.tyt.transport.service.TransportVaryService;
import com.tytrecommend.recommend.service.PreferNewService;
import junit.framework.TestCase;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2023/2/24 15:23
 */
public class TransportHistoryServiceTest extends TytTestBase {

    @Autowired
    private TransportHistoryService transportHistoryService;

    @Autowired
    private TransportService transportService;

    @Autowired
    private GoodsPushService goodsPushService;

    @Autowired
    TransportVaryService transportVaryService;

    @Autowired
    PreferNewService preferNewService;

    @Test
    public void testCreateWithTransport() {

        Long tsId = 44114059L;

        Transport byId = transportService.getById(tsId);

        TytTransportHistory withTransport = transportHistoryService.createWithTransport(byId);

        System.out.println("finished ... ");


    }

    @Test
    public void mybatisTest(){


        System.out.println("finished ... ");

    }

}