package com.tyt.plat.service.user;

import com.tyt.model.Advice;
import com.tyt.plat.test.base.TytTestBase;
import com.tyt.user.service.AdviceService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2024/5/6 11:42
 */
@Slf4j
public class DbInsertServiceTest extends TytTestBase {

    @Autowired
    private AdviceService adviceService;

    private void saveMybatis(){

        String text = "你好\uD83D\uDE13\uD83D\uDE0D\uD83D\uDE01\uD83D\uDE0C\uD83D\uDE02aabb";
        String phone = "13111111111";
        Date nowTime = new Date();

        System.out.println("finished ... ");
    }

    private void saveHibernate(){

        String text = "你好\uD83D\uDE13\uD83D\uDE0D\uD83D\uDE01\uD83D\uDE0C\uD83D\uDE02aabb";
        String phone = "13111111111";
        Date nowTime = new Date();

        Advice tytAdvice = new Advice();

        tytAdvice.setContent(text);
        tytAdvice.setCellPhone(phone);

        adviceService.add(tytAdvice);

        System.out.println("finished ... ");
    }

    @Test
    public void testUtf8Mb4(){

        try {
            this.saveMybatis();
        } catch (Exception e) {
            log.error("", e);
        }

        try {
            this.saveHibernate();
        } catch (Exception e) {
            log.error("", e);
        }

        System.out.println("finished ... ");

    }

}