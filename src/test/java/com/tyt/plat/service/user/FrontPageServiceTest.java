package com.tyt.plat.service.user;

import com.alibaba.fastjson.JSON;
import com.tyt.plat.test.base.TytTestBase;
import com.tyt.plat.vo.user.ProtocolIsPopUpVo;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @since 2024/05/14 10:53
 */
public class FrontPageServiceTest extends TytTestBase {

    @Autowired
    private FrontPageService frontPageService;

    @Test
    public void testGetPageDetail() {
        ProtocolIsPopUpVo protocolIsPopUp = frontPageService.getProtocolIsPopUp(50065L, null);
        System.out.println(JSON.toJSONString(protocolIsPopUp));
    }
}
