package com.tyt.plat.service.user;

import com.tyt.messagecenter.core.utils.DateUtil;
import com.tyt.model.User;
import com.tyt.plat.enums.GlobalStatusEnum;
import com.tyt.plat.test.base.TytTestBase;
import com.tyt.plat.vo.user.FaceUserIdentityAuthReq;
import com.tyt.plat.vo.user.UserFaceVerifyVo;
import com.tyt.user.service.TytUserIdentityAuthService;
import com.tyt.user.service.UserService;
import org.apache.commons.io.FileUtils;
import org.apache.http.entity.ContentType;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;
import java.io.File;
import java.util.Calendar;
import java.util.Date;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2024/1/17 16:58
 */
public class UserVerifyServiceTest extends TytTestBase {

    @Autowired
    private UserVerifyService userVerifyService;

    @Autowired
    private UserService userService;

    @Autowired
    private TytUserIdentityAuthService tytUserIdentityAuthService;

    private User mockUser(){

        Long userId = 1002000573L;

        String idCard = "******************";
        String userName = "许晓清";

        User user = new User();

        user.setId(userId);
        user.setIdCard(idCard);
        user.setUserName(userName);

        return user;
    }

    @Test
    public void testCheckAndRealVerify() {
        User user = this.mockUser();
        Long userId = user.getId();
        String idCard = user.getIdCard();
        String userName = user.getUserName();

        Integer integer = userVerifyService.checkAndRealVerify(userId, idCard, userName);

        System.out.println("result " + integer);

        testGetFaceToken();

        assertTrue(true);

    }

    private UserFaceVerifyVo mockGetFaceToken() {
        User user = this.mockUser();
        Long userId = user.getId();
        String idCard = user.getIdCard();
        String userName = user.getUserName();

        UserFaceVerifyVo faceToken = userVerifyService.getFaceTokenV3(userId, idCard, userName);

        return faceToken;
    }

    @Test
    public void testGetFaceToken() {

        UserFaceVerifyVo faceToken = this.mockGetFaceToken();

        String token = faceToken.getFaceToken();

        System.out.println("token " + token);
        assertTrue(true);

    }

    private MultipartFile createMultipartFile() throws Exception{

        String cardPath = "D:/source-files/test/card_img/aaaaa.png";

        File cardFile = new File(cardPath);

        String fileName = cardFile.getName();

        String contentType = "text/plain";


        byte[] fileBytes = FileUtils.readFileToByteArray(cardFile);

        // 使用 MockMultipartFile 创建 MultipartFile 实例
        MockMultipartFile multipartFile = new MockMultipartFile(fileName, fileName,
                ContentType.APPLICATION_OCTET_STREAM.getMimeType(), fileBytes);

        return multipartFile;
    }

    private void setBasePraam(FaceUserIdentityAuthReq authReq){

        authReq.setClientSign("2");
        authReq.setClientVersion("6390");

    }

    @Test
    public void testSaveFaceVerifyV3() throws Exception{

        Date nowTime = new Date();
        User user = this.mockUser();

        Long userId = user.getId();

        Date validDate = DateUtil.addTime(nowTime, Calendar.DAY_OF_MONTH, 5);

        UserFaceVerifyVo faceVerifyVo = this.mockGetFaceToken();

        String faceToken = faceVerifyVo.getFaceToken();

        User dbUser = userService.getByUserId(userId);

        FaceUserIdentityAuthReq userIdentityAuthReq = new FaceUserIdentityAuthReq();

        this.setBasePraam(userIdentityAuthReq);

        userIdentityAuthReq.setUserId(userId);
        userIdentityAuthReq.setTrueName(dbUser.getTrueName());
        userIdentityAuthReq.setIdCard(dbUser.getIdCard());

        MultipartFile mainUrlPic = this.createMultipartFile();

        userIdentityAuthReq.setMainUrlPic(mainUrlPic);
        userIdentityAuthReq.setIPhotoPic(mainUrlPic);

        userIdentityAuthReq.setIdCardValidDate(validDate);
        userIdentityAuthReq.setIdCardLongTerm(GlobalStatusEnum.yes.getCode());

        MultipartFile megliveData = this.createMultipartFile();
        userIdentityAuthReq.setFaceToken(faceToken);
        userIdentityAuthReq.setMegliveData(megliveData);

        UserFaceVerifyVo verifyVo = tytUserIdentityAuthService.saveFaceVerifyV3(userIdentityAuthReq);

        System.out.println("finished ... ");
        assertTrue(true);

    }

}