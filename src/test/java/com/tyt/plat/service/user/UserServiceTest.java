package com.tyt.plat.service.user;

import com.tyt.plat.test.base.TytTestBase;
import com.tyt.service.common.bean.ResponseMessage;
import com.tyt.service.common.tianyancha.api.CorporateBaseInfoAPI;
import com.tyt.service.common.tianyancha.bean.CorporateBaseInfoBean;
import com.tyt.util.MobileUtil;
import org.junit.Test;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2023/5/23 11:29
 */
public class UserServiceTest {

    @Test
    public void mobileTest(){

        String phone = "13501075392";
        String[] mobileAddressArr = MobileUtil.getMobileAddressArr(phone);

        System.out.println(mobileAddressArr);


    }

    @Test
    public void companyTest(){
        String keyword = "朝阳瑞河物流有限公司";
        ResponseMessage<CorporateBaseInfoBean> corporateBaseInfo = CorporateBaseInfoAPI.getCorporateBaseInfo(keyword);

        System.out.println("finished ... ");

    }

}
