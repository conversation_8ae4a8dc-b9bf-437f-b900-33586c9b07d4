package com.tyt.plat.service.user;

import lombok.extern.slf4j.Slf4j;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;

@Slf4j
public class JdbcInsertExampleTest {
    public static void main(String[] args) {

        String url = "***********************************************************************************************************";
        String user = "tyt_dev";
        String password = "tyt_dev#20200724";

        String sql = "INSERT INTO `tyt`.`tyt_advice` (`title`, `content`, `cell_phone`) " +
                "VALUES ( ?, ?, ? )";
        try (
                Connection connection = DriverManager.getConnection(url, user, password);
                PreparedStatement preparedStatement = connection.prepareStatement(sql);
        ) {

            String title = "Hello, world! 😊";
            String content = "你好\uD83D\uDE13\uD83D\uDE0D\uD83D\uDE01\uD83D\uDE0C\uD83D\uDE02aabb";
            String phone = "13111111111";

            preparedStatement.setString(1, title);
            preparedStatement.setString(2, content);
            preparedStatement.setString(3, phone);

            // Executing the statement
            int rowsInserted = preparedStatement.executeUpdate();

            log.info("add_new : {}", rowsInserted);

        } catch (SQLException e) {
            log.error("", e);
        }
        assert true;
    }
}
