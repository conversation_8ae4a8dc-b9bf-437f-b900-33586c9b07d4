package com.tyt.plat.service.user;

import com.tyt.plat.test.base.TytTestBase;
import junit.framework.TestCase;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2024/1/5 11:28
 */
public class UserAlertServiceTest extends TytTestBase {

    @Autowired
    private UserAlertService userAlertService;

    @Test
    public void testVipRedPacket() {

        Long userId = 1000002358L;

        Integer alertFlag = userAlertService.vipRedPacket(userId);

        assertNotNull(alertFlag);

    }
}