package com.tyt.plat.service.remote;

import com.tyt.plat.test.base.TytTestBase;
import com.tyt.plat.vo.mb.UserRealResponse;
import junit.framework.TestCase;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2024/1/20 14:28
 */
public class MbAccountServiceTest extends TytTestBase {

    @Autowired
    private MbAccountService mbAccountService;

    @Test
    public void testGetMbUserRealInfo() {

        Long userId = 1234L;

        UserRealResponse mbUserRealInfo = mbAccountService.getMbUserRealInfo(userId);

        assertTrue(true);

    }
}