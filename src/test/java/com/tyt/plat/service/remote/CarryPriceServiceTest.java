package com.tyt.plat.service.remote;

import com.tyt.plat.test.base.TytTestBase;
import com.tyt.plat.vo.remote.CarryPriceReq;
import com.tyt.plat.vo.remote.CarryPriceVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2022/3/21 12:31
 */
@Slf4j
public class CarryPriceServiceTest extends TytTestBase {

    @Autowired
    private CarryPriceService carryPriceService;

    @Test
    public void getCarryPrice() {

        CarryPriceReq carryPriceReq = new CarryPriceReq();
        carryPriceReq.setStartProvince("四川");
        carryPriceReq.setStartCity("成都市");
        carryPriceReq.setStartArea("双流区");
        carryPriceReq.setDestProvince("四川");
        carryPriceReq.setDestCity("凉山州");
        carryPriceReq.setDestArea("会东县");
        carryPriceReq.setGoodsName("常发CF808M玉米收割机");
        carryPriceReq.setGoodsWeight(9.5);
        carryPriceReq.setGoodsLength(10.1);
        carryPriceReq.setGoodsWide(4.02);
        carryPriceReq.setGoodsHigh(4.23);

        CarryPriceVo carryPrice = carryPriceService.getCarryPrice(carryPriceReq);

        System.out.println("ending ... ");

        assertTrue(true);  // Fest assertion
    }
}