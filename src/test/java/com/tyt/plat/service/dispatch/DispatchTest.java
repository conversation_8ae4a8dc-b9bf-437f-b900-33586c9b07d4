package com.tyt.plat.service.dispatch;

import com.tyt.plat.mapper.base.EmployeeMessageMapper;
import com.tyt.plat.test.base.TytTestBase;
import com.tyt.transport.service.TransportYMMService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2022/3/21 12:31
 */
@Slf4j
public class DispatchTest extends TytTestBase {

    @Autowired
    private TransportYMMService transportYMMService;

    @Autowired
    private EmployeeMessageMapper employeeMessageMapper;
    @Test
    public  void testConvert(){

   /* DispatchParamBean dispatchParamBean = new DispatchParamBean();
    dispatchParamBean.setCargoId(10167605088111l);
    dispatchParamBean.setSourceType(4);
    dispatchParamBean.setBackoutReasonKey("sss");
    dispatchParamBean.setBackoutReasonValue(19);
    transportYMMService.revokeBeanHandle(dispatchParamBean);*/
        transportYMMService.pushMbCargoExpireMessage(33834604L);
    }

}