package com.tyt.plat.service.help;

import com.tyt.plat.entity.base.TytCourseInfo;
import com.tyt.plat.test.base.TytTestBase;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * unit test.
 *
 * <AUTHOR>
 * @date 2023/11/29 15:51
 */
public class UserCourseServiceTest extends TytTestBase {

    @Autowired
    private UserCourseService userCourseService;

    @Test
    public void testGetMyCourseList() {
        Long userId = 1L;
        Integer type = 1;
        Integer studyFlag = null;
        Integer sortNumber = null;

        List<TytCourseInfo> myCourseList = userCourseService.getMyCourseList(userId, type, studyFlag, sortNumber);

        System.out.println("finished ... ");

        assertTrue(true);  // Fest assertion
    }
}