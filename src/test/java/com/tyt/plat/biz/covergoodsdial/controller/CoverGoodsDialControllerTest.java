package com.tyt.plat.biz.covergoodsdial.controller;

import com.alibaba.fastjson.JSON;
import com.tyt.infofee.bean.GoodsSingleDetailResultBean;
import com.tyt.infofee.service.impl.InfoFeeBusinessServiceImpl;
import com.tyt.plat.test.base.TytTestBase;
import com.tyt.transport.service.TransportMainService;
import com.tyt.transport.service.TytCoverGoodsDialConfigService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Slf4j
public class CoverGoodsDialControllerTest extends TytTestBase {

    @Autowired
    private TytCoverGoodsDialConfigService coverGoodsDialConfigService;

    @Autowired
    private InfoFeeBusinessServiceImpl infoFeeBusinessService;
    @Autowired
    private TransportMainService transportMainService;

    @Test
    public void testGetCoverGoodsDialInfo() throws Exception {
        mockMvc.perform(MockMvcRequestBuilders.get("/plat/cover/goods/dial/info")
                        .param("userId", "792466994")
                )
                .andDo((it) -> System.out.println(it.getResponse().getContentAsString()))
                .andExpect(status().isOk());
    }

    @Test
    public void testCoverGoodsCountdownReport() throws Exception {
        mockMvc.perform(MockMvcRequestBuilders.post("/plat/cover/goods/dial/countdown/report")
                        .param("userId", "147373")
                        .param("tsId", "33869412")
                        .param("countDownFinished", "true")
                        .param("reduceTimeInSeconds", "10")
                )
                .andDo((it) -> System.out.println(it.getResponse().getContentAsString()))
                .andExpect(status().isOk());
    }

    @Test
    public void testUseN() throws Exception {
        mockMvc.perform(MockMvcRequestBuilders.post("/plat/cover/goods/dial/n/use")
                        .param("userId", "792466994")
                        .param("tsId", "2")
                )
                .andDo((it) -> System.out.println(it.getResponse().getContentAsString()))
                .andExpect(status().isOk());
    }

    @Test
    public void testDetailGetCoverGoodsDialInfo() throws Exception {
        GoodsSingleDetailResultBean.CoverGoodsDialInfo coverGoodsDialInfo =
                infoFeeBusinessService.getCoverGoodsDialInfo(1002000800L, transportMainService.getTransportMainForId(33869431L));
        log.info("{}", coverGoodsDialInfo);
        Assert.assertNotNull(coverGoodsDialInfo);
    }

    @Test
    public void testGoodsSingleDetailResultBean() throws Exception {
        GoodsSingleDetailResultBean goodsInfoDetail =
                infoFeeBusinessService.getGoodsInfoDetail( 33869340L,1002000811L,1);
        log.info("{}", JSON.toJSONString(goodsInfoDetail));
        Assert.assertNotNull(goodsInfoDetail);
    }

    @Test
    public void testUseNRecord() throws Exception {
        mockMvc.perform(MockMvcRequestBuilders.get("/plat/cover/goods/dial/n/use/record")
                        .param("userId", "1")
                )
                .andDo((it) -> System.out.println(it.getResponse().getContentAsString()))
                .andExpect(status().isOk());
    }
}