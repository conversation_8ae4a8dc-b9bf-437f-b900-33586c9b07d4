package com.tyt.plat.biz.feedback.service;

import com.alibaba.fastjson.JSON;
import com.tyt.common.bean.PageData;
import com.tyt.plat.biz.feedback.pojo.*;
import com.tyt.plat.test.base.TytTestBase;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class FeedbackUserServiceImplTest extends TytTestBase {

    @Autowired
    private IFeedbackUserService feedbackUserService;

    @Test
    public void testPostFeedBack() {
        PostFeedbackReq req = new PostFeedbackReq();
        req.setOrderId(655293L);
        req.setFeedbackType(3);
        req.setLabels("10");
        req.setComment("备注");
        System.out.println(feedbackUserService.postFeedBack(req, 1000000013L, 2));
    }

    @Test
    public void testUpdateFeedBack() {
        UpdateFeedbackReq req = new UpdateFeedbackReq();
        req.setId(65L);
        req.setFeedbackType(1);
        req.setLabels("6");
        req.setComment("备注1111111111111111111");
        feedbackUserService.updateFeedBack(req, 1000000013L, 2);
    }

    @Test
    public void testDeleteFeedBack() {
        DeleteFeedbackReq req = new DeleteFeedbackReq();
        req.setId(1L);
        feedbackUserService.deleteFeedback(req, 1000000585L, 2);
    }

    @Test
    public void testGetFeedBackCount() {
        System.out.println(feedbackUserService.getUserFeedBackCount(1000000934L, 1, 2));
        System.out.println(feedbackUserService.getUserFeedBackCount(1000000934L, 1, 2));
        // System.out.println(feedbackUserService.getUserFeedBackCount(1000000268L, 1, 2));
    }

    @Test
    public void testGetFeedBackRating() {
        System.out.println(feedbackUserService.getUserFeedBackRating(1000000577L, 1));
    }

    @Test
    public void testGetUserFeedbackLabelSum() {
        System.out.println(feedbackUserService.getUserFeedbackLabelSum(null, 1, 1001000462L, 2));
    }

    @Test
    public void testGetUserFeedbackList() {
        log.info("{}", JSON.toJSONString(feedbackUserService.getUserFeedbackList(null, 1, 1000000880L, 2, null, 1, 10)));
    }

    @Test
    public void testGetUserFeedbackRatingAndLabel() {
        System.out.println(feedbackUserService.getUserFeedbackRatingAndLabel(1000000577L, 1));
    }

    @Test
    public void testGetFeedbackLabels() {
        System.out.println(feedbackUserService.getFeedbackLabels(1, 1));
        System.out.println(feedbackUserService.getFeedbackLabels(1, 2));
        System.out.println(feedbackUserService.getFeedbackLabels(1, 3));
        System.out.println(feedbackUserService.getFeedbackLabels(2, 1));
        System.out.println(feedbackUserService.getFeedbackLabels(2, 2));
        System.out.println(feedbackUserService.getFeedbackLabels(2, 3));
    }

    @Test
    public void testGetUserFeedbackTodoList() {
        System.out.println(feedbackUserService.getUserFeedbackTodoList(1000000591L, 1, 1, 10));
    }

    @Test
    public void testClaimExposureCardReward() {
        ClaimExposureCardReq req = new ClaimExposureCardReq();
        req.setId(1L);
        feedbackUserService.claimExposureCardReward(req, 1000000585L, 2);
    }

    @Test
    public void testTestClaimExposureCardReward() {
        ClaimExposureCardReq req = new ClaimExposureCardReq();
        req.setId(80L);
        feedbackUserService.claimExposureCardReward(req, 1000000646L, 2);
    }

    @Test
    public void testGetExposureCardRewardInfo() {
        System.out.println(feedbackUserService.getExposureCardRewardInfo(80L, 1000000646L, 2));
    }

    @Test
    public void testCheckCanPostFeedBack() throws Exception {
        log.info("{}", feedbackUserService.checkCanPostFeedBack(86720718L, 1000000646L, 2));
    }

    @Test
    public void testCheckInfoFeeExist() {
        log.info("{}", feedbackUserService.checkInfoFeeExist(655790L, 148139L, 2));
        log.info("{}", feedbackUserService.checkInfoFeeExist(655790L, 148139L, 1));
        log.info("{}", feedbackUserService.checkInfoFeeExist(655790L, 1000000231L, 1));
        log.info("{}", feedbackUserService.checkInfoFeeExist(655790L, 1000000231L, 2));
    }

    @Test
    public void testTestCheckInfoFeeExist() {
        log.info(JSON.toJSONString(feedbackUserService.checkInfoFeeExist(655727L, 1000000305L, 2)));
    }

    @Test
    public void testA(){
        PageData<GetUserFeedbackListVO> userFeedbackList = feedbackUserService.getUserFeedbackList(2, 2, 1002000362L, 1, null, 1, 10);
        System.out.println(userFeedbackList);
    }
}