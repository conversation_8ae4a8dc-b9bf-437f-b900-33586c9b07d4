package com.tyt.plat.biz.feedback.manager;

import com.tyt.plat.biz.feedback.pojo.GetFeedbackLabelVO;
import com.tyt.plat.entity.base.FeedbackUser;
import com.tyt.plat.test.base.TytTestBase;
import org.apache.commons.lang3.time.DateUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

public class FeedbackManagerTest extends TytTestBase {

    @Autowired
    private FeedbackManager feedbackManager;
    @Autowired
    private FeedbackLabelManager feedbackLabelManager;

    @Test
    public void testSaveFeedback() {

        Date now = new Date();

        Long userId = 1000000591L;

        for (int i = 0; i < 35; i++) {
            FeedbackUser feedbackUser = FeedbackUser.builder()
                    .createTime(now)
                    .updateTime(now)
                    .postUserId(userId)
                    .postUserType(1)
                    .receiveUserId(userId)
                    .receiveUserType(2)
                    .delFlag(false)
                    .tsOrderId((long) i)
                    .tsId((long) i)
                    .transportOrderPubUserName(String.valueOf(i))
                    .transportOrderPayUserName(String.valueOf(i))
                    .transportOrderStartPoint(String.valueOf(i))
                    .transportOrderDestPoint(String.valueOf(i))
                    .feedbackType((i % 3) + 1)
                    .feedbackDeadline(DateUtils.addDays(now, 30))
                    .needHandleOnDeadline(false)
                    .comment(String.valueOf(i))
                    .updateTimes(0)
                    .build();

            List<GetFeedbackLabelVO> feedbackLabels =
                    feedbackLabelManager.getFeedbackLabels(feedbackUser.getPostUserType(),
                            feedbackUser.getFeedbackType());

            Collections.shuffle(feedbackLabels);

            List<Long> collect = feedbackLabels.stream().limit(ThreadLocalRandom.current().nextInt(2) + 1).map(
                    GetFeedbackLabelVO::getId
            ).collect(Collectors.toList());

            feedbackManager.saveFeedback(feedbackUser, collect);
        }

        for (int i = 0; i < 35; i++) {
            FeedbackUser feedbackUser = FeedbackUser.builder()
                    .createTime(now)
                    .updateTime(now)
                    .postUserId(userId)
                    .postUserType(2)
                    .receiveUserId(userId)
                    .receiveUserType(1)
                    .delFlag(false)
                    .tsOrderId((long) i)
                    .tsId((long) i)
                    .transportOrderPubUserName(String.valueOf(i))
                    .transportOrderPayUserName(String.valueOf(i))
                    .transportOrderStartPoint(String.valueOf(i))
                    .transportOrderDestPoint(String.valueOf(i))
                    .feedbackType((i % 3) + 1)
                    .feedbackDeadline(DateUtils.addDays(now, 30))
                    .needHandleOnDeadline(false)
                    .comment(String.valueOf(i))
                    .updateTimes(0)
                    .build();

            List<GetFeedbackLabelVO> feedbackLabels =
                    feedbackLabelManager.getFeedbackLabels(feedbackUser.getPostUserType(),
                            feedbackUser.getFeedbackType());

            Collections.shuffle(feedbackLabels);

            List<Long> collect = feedbackLabels.stream().limit(ThreadLocalRandom.current().nextInt(2) + 1).map(
                    GetFeedbackLabelVO::getId
            ).collect(Collectors.toList());

            feedbackManager.saveFeedback(feedbackUser, collect);


        }
    }
}