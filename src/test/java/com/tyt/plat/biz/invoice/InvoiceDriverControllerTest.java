package com.tyt.plat.biz.invoice;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.tyt.plat.biz.invoice.pojo.*;
import com.tyt.plat.biz.invoice.serivce.IInvoiceDriverService;
import com.tyt.plat.test.base.TytTestBase;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/4/24 下午4:01
 */
public class InvoiceDriverControllerTest extends TytTestBase {

    private final String userId = "10000";
    private final String driverId = "32";
    private final Integer page = 1;
    private final Integer pageSize = 10;

    @Autowired
    private IInvoiceDriverService invoiceDriverService;

    @Test
    public void testAddDriver() throws Exception {
        for (int i = 0; i < 1; i++) {
            AddDriverDTO addDriverDTO = new AddDriverDTO();
            addDriverDTO.setName(RandomStringUtils.randomAlphabetic(5));
            addDriverDTO.setPhone(RandomStringUtils.randomNumeric(11));
            addDriverDTO.setIdCard(RandomStringUtils.randomNumeric(18));
            addDriverDTO.setQcCard(RandomStringUtils.randomNumeric(18));

            List<AddDriverCredentialDTO> credentials = Lists.newArrayList();
            for (int j = 0; j < 5; j++) {
                credentials.add(new AddDriverCredentialDTO(j + 1, RandomStringUtils.randomAlphabetic(10)));
            }

            addDriverDTO.setCredentials(JSON.toJSONString(credentials));

            MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
            for (Map.Entry<String, Object> entry :
                    JSONObject.parseObject(JSON.toJSONString(addDriverDTO)).entrySet()) {

                map.add(entry.getKey(),entry.getValue().toString());
            }

            mockMvc.perform(MockMvcRequestBuilders.post("/plat/invoice/driver/post")
                            .params(map)
                            .param("userId", userId)
                            .contentType(MediaType.APPLICATION_FORM_URLENCODED_VALUE)
                            .content(JSON.toJSONString(addDriverDTO)))
                    .andDo((it) -> System.out.println(it.getResponse().getContentAsString()));
        }
    }

    @Test
    public void testGetDriverList() throws Exception {

        mockMvc.perform(MockMvcRequestBuilders.get("/plat/invoice/driver/list")
                        .param("userId", userId)
                        //    .param("verifyStatus", "1")
                        .param("page", page.toString())
                        .param("pageSize", pageSize.toString()))
                .andDo((it) -> System.out.println(it.getResponse().getContentAsString()));
    }

    @Test
    public void testGetDriverCount() throws Exception {
        mockMvc.perform(MockMvcRequestBuilders.get("/plat/invoice/driver/count")
                        .param("userId", userId))
                .andDo((it) -> System.out.println(it.getResponse().getContentAsString()));
    }


    @Test
    public void testGetDriverDetail() throws Exception {

        mockMvc.perform(MockMvcRequestBuilders.get("/plat/invoice/driver/detail")
                        .param("id", driverId))
                .andDo((it) -> System.out.println(it.getResponse().getContentAsString()));
    }

    @Test
    public void testUpdateDriver() throws Exception {
        DriverDetailVO driverDetail = invoiceDriverService.getDriverDetail(Long.valueOf(driverId));

        UpdateDriverDTO updateDriverDTO = new UpdateDriverDTO();
        updateDriverDTO.setId(driverDetail.getId());
        updateDriverDTO.setName("测试修改" + RandomStringUtils.randomAlphanumeric(5));
        updateDriverDTO.setPhone("测试修改" + RandomStringUtils.randomAlphanumeric(5));
        updateDriverDTO.setIdCard("测试修改" + RandomStringUtils.randomAlphanumeric(14));
        updateDriverDTO.setQcCard("测试修改" + RandomStringUtils.randomAlphanumeric(14));
        updateDriverDTO.setIdCardPermanent(true);
        updateDriverDTO.setIdCardExpirationTime(new Date().getTime());

        List<DriverDetailCredentialDTO> credentials = driverDetail.getCredentials();
        if (credentials != null && !credentials.isEmpty()) {

            List<UpdateDriverCredentialDTO> collect = credentials.stream().map(it -> {
                UpdateDriverCredentialDTO driverDetailCredentialDTO = new UpdateDriverCredentialDTO();
                driverDetailCredentialDTO.setPicUrl("测试修改" + RandomStringUtils.randomAlphanumeric(10));
                driverDetailCredentialDTO.setId(it.getId());
                return driverDetailCredentialDTO;
            }).collect(Collectors.toList());
            updateDriverDTO.setCredentials(JSON.toJSONString(collect));
        }


        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        for (Map.Entry<String, Object> entry :
                JSONObject.parseObject(JSON.toJSONString(updateDriverDTO)).entrySet()) {

            map.add(entry.getKey(),entry.getValue().toString());
        }

        mockMvc.perform(MockMvcRequestBuilders.post("/plat/invoice/driver/update")
                        .param("userId", userId)
                        .params(map)
                        .contentType(MediaType.APPLICATION_FORM_URLENCODED_VALUE))
                .andDo((it) -> System.out.println(it.getResponse().getContentAsString()));
    }

    @Test
    public void testSyncDriver() {
        invoiceDriverService.syncOldDriver(Long.valueOf(userId), "测试同步" + RandomStringUtils.randomAlphanumeric(5),
                "测试同步" + RandomStringUtils.randomAlphanumeric(5));
    }
}
