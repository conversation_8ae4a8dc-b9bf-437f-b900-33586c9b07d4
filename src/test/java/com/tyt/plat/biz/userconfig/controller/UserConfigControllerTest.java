package com.tyt.plat.biz.userconfig.controller;

import com.tyt.plat.test.base.TytTestBase;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Slf4j
public class UserConfigControllerTest extends TytTestBase {

    @Test
    public void testConfigList() throws Exception {
        mockMvc.perform(MockMvcRequestBuilders.get("/plat/user/config/list")
                        .param("userId", "147373").param("configType", "AUTO_DECREASE_DIAL_TIMES")
                )
                .andDo((it) -> log.info(new String(it.getResponse().getContentAsByteArray())))
                .andExpect(status().isOk());
    }

    @Test
    public void testConfigUpdate() throws Exception {
        mockMvc.perform(MockMvcRequestBuilders.post("/plat/user/config/update")
                        .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                        .param("userId", "147373")
                        .param("type", "AUTO_DECREASE_DIAL_TIMES")
                        .param("value", "1")
                )
                .andDo((it) -> log.info(new String(it.getResponse().getContentAsByteArray())))
                .andExpect(status().isOk());
    }
}