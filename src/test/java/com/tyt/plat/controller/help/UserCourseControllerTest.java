package com.tyt.plat.controller.help;

import com.tyt.base.bean.BaseParameter;
import com.tyt.model.ResultMsgBean;
import com.tyt.plat.test.base.TytTestBase;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2023/11/28 16:00
 */
public class UserCourseControllerTest extends TytTestBase {

    @Autowired
    private UserCourseController userCourseController;

    @Test
    public void testGetAllCourseList() {
        Integer type = 1;
        Long categoryId = null;
        Integer sortNumber = null;

        ResultMsgBean resultMsgBean = userCourseController.getAllCourseList(type, categoryId, sortNumber);

        assertNotNull(resultMsgBean);

    }

    @Test
    public void testGetBannerList() {
        Integer type = 1;

        ResultMsgBean resultMsgBean = userCourseController.getBannerList(type);

        assertNotNull(resultMsgBean);

    }

    @Test
    public void testGetCategoryList() {
        Integer type = 1;

        ResultMsgBean resultMsgBean = userCourseController.getCategoryList(type);

        System.out.println(resultMsgBean);

        assertNotNull(resultMsgBean);
    }

    @Test
    public void testGetNotLearnCount() {

        Integer type = 1;
        BaseParameter baseParameter = new BaseParameter();

        ResultMsgBean result = userCourseController.getNotLearnCount(type, baseParameter);

        System.out.println(result);

        assertNotNull(result);
    }
}