package com.tyt.plat.controller.base.api;

import com.alibaba.fastjson.JSON;
import com.tyt.model.ResultMsgBean;
import com.tyt.plat.test.base.TytTestBase;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2024/1/22 13:48
 */
public class CommonApiControllerTest extends TytTestBase {

    @Autowired
    private CommonApiController commonApiController;

    @Test
    public void testIdCardOppositeOcr() {
        String url = "http://tytaudio.teyuntong.net/local/invoice/demo_import/long-term-001.png";

        ResultMsgBean resultMsgBean = commonApiController.idCardOppositeOcr(url);

        System.out.println(JSON.toJSONString(resultMsgBean));

        assertTrue(true);
    }
}