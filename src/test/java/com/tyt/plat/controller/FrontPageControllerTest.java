package com.tyt.plat.controller;

import com.alibaba.fastjson.JSON;
import com.tyt.model.ResultMsgBean;
import com.tyt.plat.controller.user.FrontPageController;
import com.tyt.plat.test.base.TytTestBase;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2024/05/10 15:24
 */
public class FrontPageControllerTest extends TytTestBase {

    @Autowired
    private FrontPageController frontPageController;

    @Test
    public void protocolIsPopUp(){
        ResultMsgBean resultMsgBean = frontPageController.protocolIsPopUp(50065L, null);
        System.out.println(JSON.toJSONString(resultMsgBean));
    }
}
