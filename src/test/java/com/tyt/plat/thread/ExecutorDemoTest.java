package com.tyt.plat.thread;

import com.tyt.messagecenter.core.utils.CommonUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2022/5/9 18:12
 */
@Slf4j
public class ExecutorDemoTest {

    static AtomicInteger count = new AtomicInteger(0);


    public static void singleTest(){
        TransportDictionaryThread dictionaryThread = new TransportDictionaryThread();

        log.info("==================== SystemInitListener_start ====================");

        ScheduledExecutorService dicExecutor = Executors.newScheduledThreadPool(4);

        dicExecutor.scheduleAtFixedRate(dictionaryThread, 1, 10, TimeUnit.SECONDS);

        log.info("---- start_transport_dictionary_thread_done .... ");
    }


    public static void main(String[] args) {
        singleTest();

        CommonUtil.loopSleep(30000, 200000, "main_wait");
    }


}
