package com.tyt.plat.mapper;

import com.tyt.acvitity.bean.ConventionActivityRankingList;
import com.tyt.model.EmployeeMessage;
import com.tyt.model.Transport;
import com.tyt.mybatis.mapper.BackendTransportMapper;
import com.tyt.mybatis.mapper.ConventionActivityMapper;
import com.tyt.mybatis.mapper.DemoMapper;
import com.tyt.plat.entity.base.TytTransportOrderSnapshot;
import com.tyt.plat.mapper.base.EmployeeMessageMapper;
import com.tyt.plat.mapper.base.TytTransportOrderSnapshotMapper;
import com.tyt.plat.test.base.TytTestBase;
import com.tyt.transport.dao.TransportDao;
import com.tyt.transport.querybean.BackendTransportBean;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2022/3/10 10:12
 */
@Slf4j
public class TkMapperTest extends TytTestBase {

    @Autowired
    private DemoMapper demoMapper;

    @Autowired
    private TytTransportOrderSnapshotMapper tytTransportOrderSnapshotMapper;
    @Autowired
    private ConventionActivityMapper conventionActivityMapper;

    @Autowired
    TransportDao transportDao;
    @Autowired
    private BackendTransportMapper backendTransportMapper;

    @Test
    public void tkMapperTest() {

        log.info("tkMapperTest_start ----- ");

        int countMt = demoMapper.countMt();

        Transport transport = transportDao.findById(44190022L);

        TytTransportOrderSnapshot tytTransportOrderSnapshot = tytTransportOrderSnapshotMapper.selectByPrimaryKey(1L);

        log.info("tkMapperTest_ending ----- ");

    }

    @Test
    public void conTest(){
        List<ConventionActivityRankingList> conventionActivityRankingLists = conventionActivityMapper.selectRankingList(16L, 2, 200);
        System.out.println(conventionActivityRankingLists);
    }

    @Test
    public void transportBackendTest(){
        BackendTransportBean backendTransportBean = backendTransportMapper.selectBackendStatusByMsgId(33828761L);
        if (Objects.nonNull(backendTransportBean)){
            backendTransportMapper.updateBackendStatusInfo(6,60,33828761L);
        }
    }

    @Autowired
    private EmployeeMessageMapper employeeMessageMapper;
    @Test
    public void testEmployee(){
        EmployeeMessage employeeMessage=new EmployeeMessage();
        employeeMessage.setTmplId(null);
        employeeMessage.setCtime(new Date());
        employeeMessage.setOperationTime(new Date());
        employeeMessage.setPushStatus(0);
        employeeMessage.setPushUserId(0l);
        employeeMessage.setPushUserName("系统");
        employeeMessage.setReadStatus(0);
        employeeMessage.setStatus(0);
        employeeMessage.setType(2669);
        employeeMessage.setUtime(new Date());
        employeeMessage.setUserId(2222222l);
        employeeMessage.setUserName(null);
        employeeMessage.setTitle("title");
        employeeMessage.setContent("summary");
        employeeMessageMapper.insertEmployeeMessage(employeeMessage);
    }
}
