package com.tyt.plat.utils;

import com.tyt.service.common.redis.RedisUtil;
import com.tyt.util.Constant;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/10/27 15:16
 */
public class RedisUtilTest {


    public static void main(String[] args) {

        String map_incr_key = "tyt:test:map_incr";

        String hash_key = "test_aaa";

        Long counter = RedisUtil.mapHincr(map_incr_key, hash_key, (int) Constant.CACHE_EXPIRE_TIME_24H);

        String mapValue = RedisUtil.getMapValue(map_incr_key, hash_key);

        System.out.println("finished ... ");


    }

}
