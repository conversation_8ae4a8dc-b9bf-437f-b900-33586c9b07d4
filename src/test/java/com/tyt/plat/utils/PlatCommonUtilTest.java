package com.tyt.plat.utils;

import com.tyt.service.common.exception.TytException;
import junit.framework.TestCase;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2023/2/28 22:34
 */
public class PlatCommonUtilTest extends TestCase {

    public void testPrintErrorInfo() {

        Exception e = new RuntimeException();

        PlatCommonUtil.printErrorInfo("test", e);

        TytException te = TytException.createException(e);

        PlatCommonUtil.printErrorInfo("test", te);

        System.out.println("finished ... ");

    }
}