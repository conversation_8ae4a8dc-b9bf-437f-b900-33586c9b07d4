package com.tyt.plat.utils;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tyt.model.TransportDone;
import org.junit.Test;

import java.math.BigDecimal;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2022/3/26 14:49
 */
public class BigDecimalSerializeTest {

    @Test
    public void serialize() throws Exception {

        TransportDone transportDone = new TransportDone();

        transportDone.setId(1L);

        transportDone.setTaskContent("taskContent-@");

        transportDone.setInfoFee(null);

        ObjectMapper mapper=new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);

        String jsonStr = mapper.writeValueAsString(transportDone);

        String s = JSON.toJSONString(transportDone);

        System.out.println("finished ...");
    }

    public void bigdecimalTest(){



    }

    public static void main(String[] args) {

        int a = 67;
        int sec = 24009;


        int c = sec % a;

        System.out.println(c);

        System.out.println("finished ... ");
    }


}