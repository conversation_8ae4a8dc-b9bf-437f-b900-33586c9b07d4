package com.tyt.plat.utils;

import com.tyt.file.controller.UploadController;
import com.tyt.messagecenter.core.utils.CommonUtil;
import com.tyt.plat.vo.base.ApiInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.util.*;

/**
 * 扫描出所有的文件上传类
 * 使用时需要移动到class下
 */
@Slf4j
@Component
public class ScanControllerUtil {

    /** 扫包路径 **/
    //final String basePackage = "edu.open.gkcloud.system.controller";
    static final String prefix = "";
    /** 只扫描Controller.class **/
    static final String controller_suffix = ".class";

    //所有的请求方式放入MAP集合中
    static Map<String,String> methodMap = new HashMap<>();
    static {
        methodMap.put(RequestMapping.class.getName(),"*");
        methodMap.put(PostMapping.class.getName(), RequestMethod.POST.name());
        methodMap.put(PutMapping.class.getName(), RequestMethod.PUT.name());
        methodMap.put(GetMapping.class.getName(), RequestMethod.GET.name());
        methodMap.put(DeleteMapping.class.getName(), RequestMethod.DELETE.name());
    }

    int insertCount = 0;


    /**
     * 获取controller class
     * @return
     * @throws Exception
     */
    public List<Class> getControllerClasses(Class bootClass) throws Exception {

        String separator = "/";//File.separator

        String rootFolder = bootClass.getProtectionDomain().getCodeSource().getLocation().getPath();

        String basePackage = bootClass.getPackage().getName() + prefix;

        log.info("==================== start scan classes ====================");

        List<Class> classList = new ArrayList<>();

        //包名转换为路径名
        String packagePath = basePackage.replace(".", separator);

        String searchPath = rootFolder + packagePath;

        Set<String> classPaths = new HashSet<>();

        this.scanClasses(new File(searchPath), classPaths);

        String tmpClassPath = rootFolder.replace("\\","/");

        for (String onePath : classPaths) {
            String onePathUrl = new File(onePath).toURI().toURL().getPath();

            onePathUrl = onePathUrl.replace("\\", "/");

            String className = onePathUrl.replace(tmpClassPath, "");

            className = className.replace(".class","").replace("/",".");

            Class clazz = Class.forName(className);

            CommonUtil.writeFileContent("D:/scan/all_class_out.log", className + "\n", true);

            boolean isController = this.checkController(clazz);
            if(isController) {
                classList.add(clazz);

                log.info("Add class : " + className);
            }else {
                log.info("Skip class : " + className);
            }
        }
        log.info("==================== end scan classes ====================");
        return classList;
    }

    /**
     * 递归获取所有controller
     * @param file
     */
    private void scanClasses(File file, Set<String> classPaths) {
        if (file.isDirectory()) {
            //文件夹递归
            File[] files = file.listFiles();
            for (File oneFile : files) {
                scanClasses(oneFile, classPaths);
            }
        } else {
            //标准文件判断是否是 controller
            if (file.getName().endsWith(controller_suffix)) {
                classPaths.add(file.getPath());
            }
        }
    }


    public String getRequestMappingUrl(RequestMapping annotRm, String name){
        String url = "";

        if(annotRm != null){
            String[] urlArray = annotRm.value();

            if(urlArray.length <= 0){
                urlArray = annotRm.path();
            }

            if(urlArray.length <= 0){
                log.error("########### : " + name + " | get baseUrl empty!");
            }else {
                url = urlArray[0];
            }
        }
        return url;
    }

    /**
     * 替换变量
     * @param text
     * @param variable
     * @return
     */
    public String repleaseVariable(String text, String variable){

        int indexStart = text.indexOf("{");

        int indexEnd = text.indexOf("}");

        String begin = text.substring(0, indexStart);

        String end = text.substring(indexEnd + 1);

        String result = begin + variable + end;

        return result;
    }


    /**
     * 获取 url 及 method
     * @param method
     * @return
     * @throws Exception
     */
    private ApiInfo getApiInfo(Method method) throws Exception {
        ApiInfo result = null;

        String requestType = null;
        String reqUrl = null;

        Annotation[] declAnons = method.getDeclaredAnnotations();

        for (Annotation annotation : declAnons) {
            String annoName = annotation.annotationType().getName();

            if (methodMap.containsKey(annoName)) {
                requestType = methodMap.get(annoName);

                if ("".equals(requestType)) {
                    RequestMapping reqMapping = (RequestMapping) annotation;
                    RequestMethod[] typeMethods = reqMapping.method();

                    if(typeMethods != null && typeMethods.length > 0) {
                        requestType = typeMethods[0].name();
                    }

                    String[] urlValues = reqMapping.value();

                    if(urlValues != null && urlValues.length > 0) {
                        reqUrl = urlValues[0];
                    }

                    if (StringUtils.isBlank(reqUrl)) {
                        String[] urlPaths = reqMapping.path();
                        if(urlPaths != null && urlPaths.length > 0) {
                            reqUrl = urlPaths[0];
                        }
                    }

                } else {
                    Method met = annotation.annotationType().getMethod("value");
                    String[] urlValues = (String[]) met.invoke(annotation);

                    if(urlValues != null && urlValues.length > 0) {
                        reqUrl = urlValues[0];
                    }

                    if (StringUtils.isBlank(reqUrl)) {

                        met = annotation.annotationType().getMethod("path");
                        urlValues = (String[]) met.invoke(annotation);

                        if(urlValues != null && urlValues.length > 0) {
                            reqUrl = urlValues[0];
                        }
                    }
                }

                if(requestType != null && reqUrl != null) {
                    result = new ApiInfo();
                    result.setUri(reqUrl);
                    result.setMethod(requestType);
                }

                break;
            }
        }

        return result;
    }

    private boolean checkController(Class oneClass) {
        boolean result = false;
        //判断是否是controller
        if(oneClass.getAnnotation(Controller.class) != null || oneClass.getAnnotation(RestController.class) != null){
            result = true;
        }
        return result;
    }

    private boolean checkMutipartFile(Method apiMethod){

        boolean result = false;

        Class<?>[] parameterTypes = apiMethod.getParameterTypes();

        for(Class oneClass: parameterTypes ){

            if(oneClass.isAssignableFrom(MultipartFile.class)){
                result = true;
                break;
            }
        }
        return result;
    }

    /**
     * 保存路径
     * @throws Exception
     */
    public void storeFunctionUrl(Class bootClass) throws Exception{
        //Class bootClass = PermissionApplication.class;

        System.out.println("\n\n======================================== [START INSERT FUNCTION] ========================================\n\n");

        insertCount = 0;

        List<Class> classList = this.getControllerClasses(bootClass);

        Long nowTime = System.currentTimeMillis();

        String outFoler = "D:/scan/";

        for(Class oneClass: classList){

            String className = oneClass.getName();

            if(oneClass.isAssignableFrom(UploadController.class)){
                System.out.println("upload ... ");
            }

            System.out.println("\n\n============================== [START CLASS] " + className + " ==============================\n\n");

            String name = oneClass.getName();

            //获取controller名称
            String apiName = className;

            //判断是否是controller
            if(this.checkController(oneClass)){

                RequestMapping annotRm = (RequestMapping)oneClass.getAnnotation(RequestMapping.class);

                String baseUrl = this.getRequestMappingUrl(annotRm, className);

                if(!baseUrl.startsWith("/")){
                    baseUrl = "/" + baseUrl;
                }

                //遍历所有方法
                Method[] methods = oneClass.getDeclaredMethods();

                for(Method oneMeth : methods){
                    String methodName = oneMeth.getName();

                    ApiInfo apiInfo = getApiInfo(oneMeth);

                    if(apiInfo == null) {
                        continue;
                    }

                    String reqUrl = apiInfo.getUri();
                    String requestType = apiInfo.getMethod();

                    String fullUrl = CommonUtil.joinPath(baseUrl, reqUrl);
                    fullUrl = fullUrl.trim();

                    //fullUrl = new File(fullUrl).getPath();
                    //完整url
                    //fullUrl = fullUrl.replace("\\", "/").trim();

                    if(!this.checkMutipartFile(oneMeth)){
                        continue;
                    }

                    //所有接口都需要入库
                    System.out.println("==================== [START METHOD] " + methodName + " ====================");

                    String logMsg = className + " - " + oneMeth.getName();

                    //获取方法名
                    String methodApiName = methodName;

                    String apiFullName = apiName + "-" + methodApiName;

                    String[] lineArray = {apiName, methodApiName, fullUrl};

                    String urlLine = StringUtils.join(lineArray, "\t");

                    CommonUtil.writeFileContent(outFoler + "multipart_file_url.log", urlLine + "\n", true);

                    System.out.println("==================== [FINISH METHOD] " + methodName + " ====================");

                }

            }

            System.out.println("\n\n============================== [END CLASS] " + className + " ==============================\n\n");
        }

        System.out.println("\n\n======================================== [ALL DONE] all function save done. [" + insertCount + "] new record added. ========================================\n\n");

        insertCount = 0;
    }

    private void classTest(){

        Class<UploadController> uploadControllerClass = UploadController.class;

        Method[] declaredMethods = uploadControllerClass.getDeclaredMethods();

        for(Method oneMethod: declaredMethods){

            String name = oneMethod.getName();

            if(name.equals("img")){

                boolean b = this.checkMutipartFile(oneMethod);

                System.out.println(b);

            }

        }

    }

    public static void main(String[] args) {

        ScanControllerUtil scanUtil = new ScanControllerUtil();

        try {
            scanUtil.storeFunctionUrl(ScanControllerUtil.class);
            System.out.println("finished ... ");

        } catch (Exception e) {
            e.printStackTrace();
        }

        System.out.println("finished ... ");
    }

}
