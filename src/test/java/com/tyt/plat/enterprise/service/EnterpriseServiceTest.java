package com.tyt.plat.enterprise.service;

import com.alibaba.fastjson.JSON;
import com.tyt.plat.enterprise.pojo.vo.VerifySaveInfoReq;
import com.tyt.plat.test.base.TytTestBase;
import com.tyt.plat.utils.http.HttpClientUtil;
import com.tyt.plat.vo.enterprise.EnterpriseContractVo;
import com.tyt.plat.vo.esign.LocalSafeSignPDF3rdVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2024/2/29 14:36
 */
@Slf4j
public class EnterpriseServiceTest extends TytTestBase {
    Long userId = 1002000279L;

    String phone = "13501075392";

    @Autowired
    private EnterpriseService enterpriseService;

    @Test
    public void testTerminateContract() {

        enterpriseService.terminateContract(userId);

        assertTrue(true);

    }

    @Test
    public void testRejectEnterpriseVerify() {
        enterpriseService.rejectEnterpriseVerify(userId);

        assertTrue(true);
    }

    @Test
    public void testGetContractPreview() {

        EnterpriseContractVo contractPreview = enterpriseService.getContractPreview(userId);

        log.info("contract_preview : {}", JSON.toJSONString(contractPreview));

        assertTrue(true);

    }

    @Test
    public void testAgreeSignContract() {
        EnterpriseContractVo contractVo = enterpriseService.agreeSignContract(userId);

        log.info("testAgreeSignContract : {}", JSON.toJSONString(contractVo));

        assertTrue(true);
    }

    @Test
    public void testSignValidSend() throws Exception{

        enterpriseService.signValidSend(userId, phone);

        System.out.println("send valid code ... ");

        String validCode = "123456";

        enterpriseService.signValidCheck(userId, validCode);

        for(int i = 0; i< 30; i++){

            EnterpriseContractVo enterpriseSign = enterpriseService.getEnterpriseSign(userId);

            log.info("sign_status : {}", JSON.toJSONString(enterpriseSign));

            Thread.sleep(1000);
        }

        assertTrue(true);

    }

    @Test
    public void testDoSelfSignSeal() {

        LocalSafeSignPDF3rdVO signPDF3rdVO = new LocalSafeSignPDF3rdVO();

        String pdfUrl = "https://tytaudio.teyuntong.net/test/plat/enterprise/blank/2024/05/15/contract_A9593FD6AF22433F8A5924B61AA3574D.pdf";
        String pdfBase64 = HttpClientUtil.downloadBase64(pdfUrl);

        signPDF3rdVO.setPdfBase64(pdfBase64);

        LocalSafeSignPDF3rdVO localSafeSignPDF3rdVO = enterpriseService.doSelfSignSeal(signPDF3rdVO);

        assert true;

    }

    @Test
    public void testSaveInfo() {

        Long userId = 1002000279L;
        String enterpriseAuthLicenseUrl = "";
        String enterpriseAuthPath = "";
        Integer clientSign = 21;

        VerifySaveInfoReq verifySubmitInfoReq = new VerifySaveInfoReq();

        enterpriseService.saveInfo(userId, verifySubmitInfoReq, clientSign);

        System.out.println("finished ... ");

        assert true;
    }

    @Test
    public void testAddGmvRate() {
        Long enterpriseId = 15L;

        Map<String, Object> pdfFormMap = new HashMap<>();

        enterpriseService.addGmvRate(pdfFormMap, enterpriseId);

        System.out.println("finished ... ");
        assert true;
    }
}