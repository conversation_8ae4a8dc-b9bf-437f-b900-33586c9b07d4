package com.tyt.plat.test.other;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.tyt.manbang.bean.response.AccountData;
import com.tyt.messagecenter.core.utils.CommonUtil;
import com.tyt.messagecenter.core.utils.DateUtil;
import com.tyt.model.ResultMsgBean;
import com.tyt.plat.entity.base.TytCourseInfo;
import org.apache.commons.io.FileUtils;
import org.junit.Test;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2022/6/15 11:20
 */
public class FastJsonTest {

    public static void test1() throws Exception{

        String path = "D:/source-files/test/java/demoJson.txt";

        String content = FileUtils.readFileToString(new File(path), "UTF-8");

        AccountData accountData =JSON.parseObject(content, AccountData.class);

        String s = JSON.toJSONString(accountData);

        System.out.println(s);
    }
    public static void test(){

        String[] versionArray = {"6150", "6250", "6140", "6153"};

        versionArray = new String[]{""};

        Set<String> versionSet = Arrays.stream(versionArray).collect(Collectors.toSet());

        boolean contains = versionSet.contains("6140");

        System.out.println("finished ... ");

    }

    @Test
    public void resultMsgJson() {

        List<TytCourseInfo> courseList = new ArrayList<>();

        TytCourseInfo courseInfo = new TytCourseInfo();

        courseList.add(courseInfo);

        ResultMsgBean resultMsgBean = ResultMsgBean.successResponse(courseList);

        String jsonString = JSON.toJSONString(resultMsgBean, SerializerFeature.WriteMapNullValue);

        assert (jsonString != null);

    }

    public static void main(String[] args) throws Exception {

        String dateText = "2024-02-29 11:16:50";

        Date date = DateUtil.parseDate(dateText, DateUtil.date_time_format);

        String value = DateUtil.dateToString(date, DateUtil.date_time_format);

        System.out.println(value);

        test();
    }

}
