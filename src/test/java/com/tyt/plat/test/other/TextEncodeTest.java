package com.tyt.plat.test.other;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.junit.Test;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.nio.ByteBuffer;
import java.nio.CharBuffer;
import java.nio.charset.*;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2023/5/19 9:54
 */
@Slf4j
public class TextEncodeTest {

    public String testDecode(String text) {

        if(text == null){
            return null;
        }

        String result = text;

        CharsetDecoder decoder = StandardCharsets.UTF_8.newDecoder();

        decoder.onMalformedInput(CodingErrorAction.REPLACE);

        decoder.onUnmappableCharacter(CodingErrorAction.REPLACE);

        ByteBuffer bb = ByteBuffer.wrap(text.getBytes(StandardCharsets.UTF_8));

        Char<PERSON><PERSON>er parsed = null;
        try {
            parsed = decoder.decode(bb);

            result = parsed.toString();
        } catch (CharacterCodingException e) {
            e.printStackTrace();
        }

        boolean equals = text.equals(result);

        System.out.println(equals);

        return result;
    }

    public String clearWithReplace(String text){
        if(text == null){
            return null;
        }

        String reg = "[\ud800\udc00-\udbff\udfff\ud800-\udfff]";

        String result = text.replaceAll(reg, "");

        boolean equals = text.equals(result);
        //System.out.println(equals);

        return result;
    }

    public String clearWithEncode(String text){
        if(text == null){
            return null;
        }

        Charset charset = Charset.forName("GBK");

        String result = new String(text.getBytes(charset), charset);

        boolean equals = text.equals(result);
        System.out.println(equals);

        return result;
    }


    public void testAllClear(String content){

        //decode
        String result = testDecode(content);

        //unicode
        result = clearWithReplace(content);

        //encode
        result = clearWithEncode(content);

        System.out.println(result);

    }

    @Test
    public void testUnknowText() throws Exception{

        String a1 = "\uD840\uDC86";

        String a2 = "厂";

        byte[] b1 = a1.getBytes(StandardCharsets.UTF_8);
        byte[] b2 = a2.getBytes(StandardCharsets.UTF_8);

        testAllClear(a1);

        testAllClear(a2);

        String text = "D:/source-files/test/text/code.txt";

        String content = FileUtils.readFileToString(new File(text), StandardCharsets.UTF_8);

        testAllClear(content);

        System.out.println("finished ... ");

    }

    @Test
    public void checkClearError() throws Exception{

        String path = "D:/source-files/test/text/tyt_transport_main_0419.sql";

        File tsFile = new File(path);

        FileInputStream finput = new FileInputStream(tsFile);
        InputStreamReader inputReader = new InputStreamReader(finput, StandardCharsets.UTF_8);
        BufferedReader bufferedReader = new BufferedReader(inputReader);

        int i = 0;
        String line = null;
        while ((line = bufferedReader.readLine()) != null){

            //String result = clearWithReplace(line);
            String result = clearWithEncode(line);

            //System.out.println(result);
            if(!result.equals(line)){

                log.error("error content : " + line);
                log.error("checkClearError : " + i);

            }

            i++;
        }

        finput.close();

        log.info("finished ... ");
    }


}
