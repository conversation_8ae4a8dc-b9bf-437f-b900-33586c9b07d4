package com.tyt.plat.test.other;

/**
 * <AUTHOR>
 * 正则单元测试类
 * @date 2022/6/15 11:20
 */
public class RegeTest {

    
    public static void main(String[] args) throws Exception {

        int i1 = 55555;
        Integer i2 = new Integer(55555);

        boolean b1 = (i1 == i2);

        Integer i3 = new Integer(55555);
        boolean b2 = (i2 == i1);

        boolean b21 = (i3 == i1);

        boolean b3 = (i2 == i3);

        boolean b4 = (i2.equals(i3));

        System.out.println("finished ... ");
    }

}
