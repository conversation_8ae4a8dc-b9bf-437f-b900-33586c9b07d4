import com.tyt.model.Car;
import com.tyt.util.TimeUtil;
import org.apache.commons.lang3.StringUtils;

import java.time.Instant;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @since 2024/01/09 18:04
 */
public class TestMain {
    public static void main(String[] args) {
        String s="2022年12月黑B";
        int mm = s.indexOf("月");
        if (mm == -1){

        }
        System.out.println(mm);
        String substring = s.substring(0, mm + 1);
        System.out.println(substring);

        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy年MM月");
        //LocalDate formatterDate = LocalDate.parse("2022年09月19日",dateTimeFormatter);
        //LocalTime parse = LocalTime.parse(substring, dateTimeFormatter);
        //System.out.println(parse);
        System.out.println(getDateInfo(s));
        System.out.println(getYearInfo(s));
        System.out.println(getYearBySubString(s));
        System.out.println(getMonthBySubString(s));


        List<Long> carIds = new ArrayList<>();
        carIds.add(4831L);
        List<Long> carOwnerCarIds = new ArrayList<>();
        carOwnerCarIds.add(4831L);
        boolean b =  carOwnerCarIds.containsAll(carIds);
        System.out.println(b);
        if (carOwnerCarIds.containsAll(carIds)){
            System.out.println(111111);
        }


        // 获取指定年、月
        int year = 2024;
        int month = 2;

        // 获取指定年、月的LocalDate
        LocalDate date = LocalDate.of(year, month, 1);

        // 获取指定年、月的最后一天
        LocalDate lastDayOfMonth = date.withDayOfMonth(date.lengthOfMonth());

        // 打印指定年、月的最后一天的日期
        System.out.println("指定年、月的最后一天的日期是：" + lastDayOfMonth.toString());

        long timestamp = System.currentTimeMillis(); // 获取当前的时间戳

        Instant instant = Instant.ofEpochMilli(timestamp); // 将时间戳转换为Instant对象
        Date date2 = Date.from(instant); // 将Instant对象转换为Date对象

        System.out.println("Timestamp: " + timestamp);
        System.out.println("Converted Date: " + date2);


        Car car = new Car();
        Map<String,String> params = new HashMap<>();
        params.put("headDrivingExpiredTime","1710211796000");
        params.put("tailDrivingExpiredTime","1710211796000");
        if (org.apache.commons.lang.StringUtils.isNotBlank(params.get("headDrivingExpiredTime"))) {
            Long headDrivingExpiredTimeL = Long.valueOf(params.get("headDrivingExpiredTime"));
            if (headDrivingExpiredTimeL > 0) {
                Date headDrivingExpiredTime = TimeUtil.timeStampToDate(headDrivingExpiredTimeL);
                car.setHeadDrivingExpiredTime(headDrivingExpiredTime);
            }
        }

        if (org.apache.commons.lang.StringUtils.isNotBlank(params.get("tailDrivingExpiredTime"))) {
            Long tailDrivingExpiredTimeL = Long.valueOf(params.get("tailDrivingExpiredTime"));
            if (tailDrivingExpiredTimeL > 0) {
                Date tailDrivingExpiredTime = TimeUtil.timeStampToDate(tailDrivingExpiredTimeL);
                if (Objects.nonNull(tailDrivingExpiredTime)) {
                    car.setTailDrivingExpiredTime(tailDrivingExpiredTime);
                }
            }
        }

        System.out.println(car);
    }


    public static String getDateInfo(String input){
        String pattern = "\\d{4}年\\d{2}月";

        Pattern regex = Pattern.compile(pattern);
        Matcher matcher = regex.matcher(input);

        if (matcher.find()) {
            String date = matcher.group();
            System.out.println("提取的日期信息为：" + date);
            return date;
        } else {
            System.out.println("未找到日期信息");
            return null;
        }
    }

    public static String getYearInfo(String input){
        String pattern = "\\d{4}";

        Pattern regex = Pattern.compile(pattern);
        Matcher matcher = regex.matcher(input);

        if (matcher.find()) {
            String date = matcher.group();
            System.out.println("提取的日期信息为：" + date);
            return date;
        } else {
            System.out.println("未找到日期信息");
            return null;
        }
    }

    public static Integer getYearBySubString(String input){
        if (StringUtils.isBlank(input)){
            return null;
        }
        try {
            int yearIndex = input.indexOf("年");
            String year = input.substring(yearIndex - 4, yearIndex);
            return Integer.parseInt(year);
        }catch (Exception e){

        }
        return null;
    }

    public static Integer getMonthBySubString(String input) {
        if (StringUtils.isBlank(input)) {
            return null;
        }
        try {
            int yearIndex = input.indexOf("月");
            String month = input.substring(yearIndex - 2, yearIndex);
            return Integer.parseInt(month);
        } catch (Exception e) {

        }
        return null;
    }
}
