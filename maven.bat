@echo off
cd %~dp0
@title ����ѡ��

@color e0

:mainmenu

@echo **************************** maven����ѡ��˵� ****************************

@echo ѡ�����

@echo 1:   mvn clean install -Dmaven.test.skip=true 

@echo 2:   mvn clean package  deploy -Dmaven.test.skip=true 

@echo 3:   mvn clean 

@echo 4:   mvn install

@echo 5:   mvn install -Dmaven.test.skip=true 

@echo 6:   mvn package

@echo 7:   mvn package -Dmaven.test.skip=true 

@echo 8:   mvn eclipse:myeclipse




@set /p choice=����������ѡ��󰴻س�:

@set choice=%choice:~0,1%

@if /I "%choice%"=="1" goto product1

@if /I "%choice%"=="2" goto product2

@if /I "%choice%"=="3" goto product3

@if /I "%choice%"=="4" goto product4

@if /I "%choice%"=="5" goto product5

@if /I "%choice%"=="6" goto product6

@if /I "%choice%"=="7" goto product7

@if /I "%choice%"=="8" goto product8


@goto end

 

:product1
@echo mvn clean install -Dmaven.test.skip=true
cmd /C mvn clean install -Dmaven.test.skip=true 
@goto end

:product2
@echo mvn clean package  deploy -Dmaven.test.skip=true 
cmd /C mvn clean package  deploy -Dmaven.test.skip=true 
@goto end

:product3
@echo mvn clean 
cmd /C mvn clean 
@goto end
:product4
@echo mvn install
cmd /C mvn install
@goto end
:product5
@echo mvn install  -Dmaven.test.skip=true 
cmd /C mvn install  -Dmaven.test.skip=true 
@goto end

:product6
@echo  mvn package
cmd /C mvn package
@goto end

:product7
@echo  mvn package -Dmaven.test.skip=true 
cmd /C mvn package -Dmaven.test.skip=true 
@goto end

:product8
@echo mvn eclipse:myeclipse
cmd /C mvn eclipse:myeclipse
@goto end

:end

@pause